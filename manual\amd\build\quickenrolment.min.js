define("enrol_manual/quickenrolment",["exports","core_table/dynamic","core/str","core/toast","core/config","core/fragment","core/modal_events","core/notification","jquery","core/pending","core/prefetch","core/modal_save_cancel"],(function(_exports,DynamicTable,Str,Toast,_config,_fragment,_modal_events,_notification,_jquery,_pending,_prefetch,_modal_save_cancel){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}
/**
   * Quick enrolment AMD module.
   *
   * @module     enrol_manual/quickenrolment
   * @copyright  2016 Damyon Wiese <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,DynamicTable=_interopRequireWildcard(DynamicTable),Str=_interopRequireWildcard(Str),Toast=_interopRequireWildcard(Toast),_config=_interopRequireDefault(_config),_fragment=_interopRequireDefault(_fragment),_modal_events=_interopRequireDefault(_modal_events),_notification=_interopRequireDefault(_notification),_jquery=_interopRequireDefault(_jquery),_pending=_interopRequireDefault(_pending),_prefetch=_interopRequireDefault(_prefetch),_modal_save_cancel=_interopRequireDefault(_modal_save_cancel);const Selectors_cohortSelector="#id_cohortlist",Selectors_triggerButtons=".enrolusersbutton.enrol_manual_plugin [type='submit']",Selectors_unwantedHiddenFields="input[value='_qf__force_multiselect_submission']",Selectors_buttonWrapper='[data-region="wrapper"]',getBodyForContext=contextId=>_fragment.default.loadFragment("enrol_manual","enrol_users_form",contextId,{}),registerEventListeners=contextId=>{document.addEventListener("click",(e=>{if(e.target.closest(Selectors_triggerButtons))return e.preventDefault(),void showModal((element=>{const wrapper=element.closest(Selectors_buttonWrapper);return DynamicTable.getTableFromId(wrapper.dataset.tableUniqueid)})(e.target),contextId)}))},showModal=(dynamicTable,contextId)=>{const pendingPromise=new _pending.default("enrol_manual/quickenrolment:showModal");return _modal_save_cancel.default.create({large:!0,title:Str.get_string("enrolusers","enrol_manual"),body:getBodyForContext(contextId),buttons:{save:Str.get_string("enrolusers","enrol_manual")},show:!0}).then((modal=>(modal.getRoot().on(_modal_events.default.save,(e=>{e.preventDefault(),modal.getRoot().find("form").submit()})),modal.getRoot().on("submit","form",(e=>{e.preventDefault(),submitFormAjax(dynamicTable,modal)})),modal.getRoot().on(_modal_events.default.hidden,(()=>{modal.destroy()})),modal))).then((modal=>Promise.all([modal,modal.getBodyPromise()]))).then((_ref=>{let[modal,body]=_ref;return body.get(0).querySelector(Selectors_cohortSelector)?modal.setSaveButtonText(Str.get_string("enroluserscohorts","enrol_manual")).then((()=>modal)):modal})).then((modal=>(pendingPromise.resolve(),modal))).catch(_notification.default.exception)},submitFormAjax=(dynamicTable,modal)=>{const form=modal.getRoot().find("form");form.get(0).querySelectorAll(Selectors_unwantedHiddenFields).forEach((hiddenField=>hiddenField.remove())),modal.hide(),modal.destroy(),_jquery.default.ajax("".concat(_config.default.wwwroot,"/enrol/manual/ajax.php?").concat(form.serialize()),{type:"GET",processData:!1,contentType:"application/json"}).then((response=>{if(response.error)throw new Error(response.error);return response.count})).then((count=>Promise.all([Str.get_string("totalenrolledusers","enrol",count),DynamicTable.refreshTableContent(dynamicTable)]))).then((_ref2=>{let[notificationBody]=_ref2;return notificationBody})).then((notificationBody=>Toast.add(notificationBody))).catch((error=>{_notification.default.addNotification({message:error.message,type:"error"})}))};_exports.init=_ref3=>{let{contextid:contextid}=_ref3;registerEventListeners(contextid),_prefetch.default.prefetchStrings("enrol_manual",["enrolusers","enroluserscohorts"]),_prefetch.default.prefetchString("enrol","totalenrolledusers")}}));

//# sourceMappingURL=quickenrolment.min.js.map