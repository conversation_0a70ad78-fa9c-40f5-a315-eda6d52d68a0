<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Subscription management controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use \Stripe\StripeClient;

/**
 * Controller for subscription management functionality
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class subscription_controller {

    /**
     * Stripe client for communication with Stripe
     */
    private $stripe;

    /**
     * Plugin instance
     */
    private $plugin;

    /**
     * Constructor
     */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->stripe = new StripeClient(get_config('enrol_stripepayment', 'secretkey'));
    }

    /**
     * Get subscriptions for user or all users (admin)
     * 
     * @param int $userid User ID (0 for all users if admin)
     * @param bool $is_admin Whether the user is admin
     * @return array Array of subscription data
     */
    public function get_subscriptions($userid = 0, $is_admin = false) {
        global $DB, $USER;

        $conditions = ['product_type' => 'service'];
        
        if ($is_admin && $userid == 0) {
            // Admin viewing all subscriptions
            $subscriptions = $DB->get_records('enrol_stripepaymentpro', $conditions);
        } else {
            // User viewing their own subscriptions or admin viewing specific user
            $conditions['userid'] = $userid ?: $USER->id;
            $subscriptions = $DB->get_records('enrol_stripepaymentpro', $conditions);
        }

        $stripe_subscriptions = [];
        $subscriptions_by_id = [];

        foreach ($subscriptions as $sub) {
            $subscriptions_by_id[$sub->subscription_id] = $sub;
            try {
                $subscription = $this->stripe->subscriptions->retrieve($sub->subscription_id);
                $stripe_subscriptions[] = $subscription;
            } catch (\Exception $e) {
                \core\notification::error($e->getMessage());
            }
        }

        return [
            'stripe_subscriptions' => $stripe_subscriptions,
            'subscriptions_by_id' => $subscriptions_by_id
        ];
    }

    /**
     * Cancel a subscription
     * 
     * @param string $subscription_id Stripe subscription ID
     * @param array $allowed_subscriptions Array of allowed subscription IDs for security
     * @return bool Success status
     */
    public function cancel_subscription($subscription_id, $allowed_subscriptions = []) {
        if (!in_array($subscription_id, $allowed_subscriptions)) {
            throw new \moodle_exception('invalidsubid', 'enrol_stripepaymentpro');
        }

        try {
            // Cancel the subscription immediately
            $subscription = $this->stripe->subscriptions->cancel($subscription_id, ['at_period_end' => false]);

            // Check the status after cancellation
            if ($subscription->status == 'canceled') {
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }
    }

    /**
     * Prepare subscription data for template rendering
     * 
     * @param array $stripe_subscriptions Stripe subscription objects
     * @param array $subscriptions_by_id Local subscription data indexed by ID
     * @param bool $is_admin Whether viewing as admin
     * @return array Template data
     */
    public function prepare_template_data($stripe_subscriptions, $subscriptions_by_id, $is_admin = false) {
        global $DB;

        $subscription_data = [];

        foreach ($stripe_subscriptions as $subscription) {
            $local_sub = $subscriptions_by_id[$subscription->id];
            $user = $DB->get_record('user', ['id' => $local_sub->userid]);
            $course = $DB->get_record('course', ['id' => $local_sub->courseid]);

            $data = [
                'subscription_id' => $subscription->id,
                'course_name' => $course->fullname,
                'status' => $subscription->status,
                'start_date' => userdate($subscription->current_period_start),
                'next_payment' => userdate($subscription->current_period_end),
                'is_active' => $subscription->status == 'active',
                'can_cancel' => $subscription->status == 'active'
            ];

            if ($is_admin) {
                $data['username'] = $user->username;
            }

            $subscription_data[] = $data;
        }

        return [
            'subscriptions' => $subscription_data,
            'is_admin' => $is_admin,
            'has_subscriptions' => !empty($subscription_data)
        ];
    }
}
