{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepayment/enrol_page

    Contents of the enrolment widget on the course enrolment page

    Example context (json):
    {
        "hascost": true
        "cost": "10.00",
        "currency": "USD",
        "coursename": "Course Name",
        "instanceid": "1",
        "wwwroot": "https://example.com"
        "enablecouponsection": true
    }
}}
<div class="stripepayment-paymentcontainer">
    <div class="stripepayment-currencybox">
        <span class="currencyselected">
            {{currency}} {{cost}}
        </span>
    </div>

    <div class="stripepayment-item">
        <div class="stripepayment-itemtitle">{{coursename}}</div>
        <div class="stripepayment-itemprice">{{currency}} {{cost}}</div>
    </div>
    <p class="stripepayment-itemsubtitle">{{coursename}}</p>

    <div class="stripepayment-line"></div>

    <div class="stripepayment-subtotal">
        <span>{{# str }} enrolnow, enrol_stripepayment {{/ str }}</span>
        <span id="subtotalamount-{{instanceid}}">{{currency}} {{cost}}</span>
    </div>

    <!-- Coupon input section -->
    {{#enablecouponsection}}
    <div class="stripepayment-couponsection">
        <div class="stripepayment-coupon-input-group" id="couponinputgroup">
            <input type="text" id="coupon-{{instanceid}}" placeholder="{{# str }} entercoupon, enrol_stripepayment {{/ str }}" />
            <button id="apply-{{instanceid}}" class="stripepayment-applybtn">{{# str }} apply, enrol_stripepayment {{/ str }}</button>
        </div>
        <div id="showmessage-{{instanceid}}" class="stripepayment-couponmessage"></div>
    </div>
    {{/enablecouponsection}}

    <!-- Discount section (hidden by default, shown when coupon is applied) -->
    <div id="discountsection-{{instanceid}}" style="display: none;">
        <div class="stripepayment-discount">
            <span class="stripepayment-tag" id="discounttag-{{instanceid}}">{{# str }} discount, enrol_stripepayment {{/ str }}</span>
            <span class="stripepayment-discountamount" id="discountamountdisplay-{{instanceid}}">-{{currency}} 0.00</span>
        </div>
        <p class="stripepayment-discountnote" id="discountnote-{{instanceid}}">{{# str }} discountapplied, enrol_stripepayment {{/ str }}</p>
    </div>

    <div class="stripepayment-total" id="total-{{instanceid}}">
        <strong>{{# str }} totaldue, enrol_stripepayment {{/ str }}</strong>
        <strong id="totalamount-{{instanceid}}">{{currency}} {{cost}}</strong>
    </div>

    <!-- Container for displaying payment response/errors -->
    <div id="paymentresponse-{{instanceid}}" class="stripepayment-error-message" style="display: none;"></div>

    <!-- Single enrollment button that handles paid enrollment -->
    <div id="enrollmentbutton-{{instanceid}}">
        <button id="enrolbutton-{{instanceid}}" class="stripepayment-pay-btn">
            {{# str }} enrolnow, enrol_stripepayment {{/ str }}
        </button>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>

<style>
    .stripepayment-pay-btn {
        margin-top: 18px;
        width: 100%;
        background-color: {{#enrolbtncolor}}{{enrolbtncolor}}{{/enrolbtncolor}}{{^enrolbtncolor}}#0070f3{{/enrolbtncolor}};
        color: white;
        border: none;
        padding: 12px 0;
        font-size: 16px;
        font-weight: bold;
        border-radius: 6px;
        cursor: pointer;
    }

    .stripepayment-pay-btn:hover {
        background-color: {{#enrolbtncolor}}{{enrolbtncolor}}{{/enrolbtncolor}}{{^enrolbtncolor}}#005fd1{{/enrolbtncolor}};
        filter: brightness(0.9);
    }

    .stripepayment-pay-btn:active {
        transform: translateY(1px);
        background-color: {{#enrolbtncolor}}{{enrolbtncolor}}{{/enrolbtncolor}}{{^enrolbtncolor}}#004bb5{{/enrolbtncolor}};
        filter: brightness(0.8);
    }
    </style>
