<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once("$CFG->libdir/externallib.php");
require_once("$CFG->libdir/enrollib.php");
require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
require_once($CFG->dirroot.'/enrol/stripepaymentpro/classes/helper/webhook_helper.php');
require_once($CFG->dirroot.'/enrol/stripepayment/externallib.php');
use enrol_stripepaymentpro\helper\webhook_helper;
use \Stripe\Stripe as Stripe;
use \Stripe\Checkout\Session as Session;
use \Stripe\Coupon as Coupon;
use \Stripe\Price as Price;


/**
 * Stripe enrolment plugin.
 *
 * External library for webservices calls
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class moodle_enrol_stripepaymentpro_external extends moodle_enrol_stripepayment_external {

	public static function stripepaymentpro_applycoupon_parameters() {
        return new external_function_parameters([
            'couponinput' => new external_value(PARAM_RAW, 'The coupon id to operate on'),
            'instanceid' => new external_value(PARAM_RAW, 'Update instance id'),
        ]);
    }

    public static function stripepaymentpro_applycoupon_returns() {
        parent::stripepayment_applycoupon_returns();
    }

    public static function stripepaymentpro_applycoupon($couponinput, $instanceid) {
        global $DB, $USER;
        $instance = $DB->get_record('enrol', ['id' => $instanceid, 'enrol' => 'stripepaymentpro'], '*', MUST_EXIST);
        $stripeproductid = $instance->customtext2 ?? null;
        $coupon = $DB->get_record('enrol_stripepro_coupons', ['couponid' => $couponinput], '*', IGNORE_MISSING);
        if (!$coupon) {
            $coupon = $DB->get_record_sql("
                SELECT *
                FROM {enrol_stripepro_coupons}
                WHERE LOWER(coupon_name) = LOWER(:coupon_name)
                LIMIT 1
            ", ['coupon_name' => $couponinput]);
        }
        if (!$coupon) {
            throw new moodle_exception('invalidcoupon', 'enrol_stripepaymentpro', '', $couponinput);
        }
            if (!empty($coupon->stripe_product_id) && $coupon->stripe_product_id !== $stripeproductid) {
            throw new moodle_exception('couponnotvalidforthisproduct', 'enrol_stripepaymentpro', '', $coupon->couponid);
        }
        return self::calculate_subscription_discount($coupon->couponid, $instanceid);
    }

    /**
     * Calculate discount for subscription products based on full subtotal
     * @param string $couponid Stripe coupon ID
     * @param int $instanceid Instance ID
     * @return array Discount calculation result
     */
    private static function calculate_subscription_discount($couponid, $instanceid) {
        global $DB;

        // Enhanced input validation
        if (empty($couponid) || trim($couponid) === '') {
            throw new moodle_exception('invalidcoupon', 'enrol_stripepaymentpro', '', 'Coupon code cannot be empty');
        }

        if (!is_numeric($instanceid) || $instanceid <= 0) {
            throw new moodle_exception('invalidcoupon', 'enrol_stripepaymentpro', '', 'Invalid instance ID format');
        }

        $plugin = enrol_get_plugin('stripepaymentpro');
        $instance = $DB->get_record('enrol', ['id' => $instanceid, 'enrol' => 'stripepaymentpro'], '*', MUST_EXIST);

        // Validate Stripe configuration
        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        if (empty($secretkey)) {
            throw new moodle_exception('invalidcoupon', 'enrol_stripepaymentpro', '', 'Stripe configuration incomplete');
        }

        // Get pricing information
        $sign_up_fee = (float)$instance->cost > 0 ? (float)$instance->cost : (float)$plugin->get_config('cost');
        $currency = $instance->currency ? $instance->currency : 'USD';
        // Get recurring fee if exists
        $renewal_fee = 0;
        if (!empty($instance->customtext4)) {
            try {
                \Stripe\Stripe::setApiKey($secretkey);
                $renewalcostobject = \Stripe\Price::retrieve($instance->customtext4);
                $renewal_fee = $renewalcostobject->unit_amount / $plugin->get_fractional_unit_amount($currency);
            } catch (\Stripe\Exception\InvalidRequestException $e) {
                if (strpos($e->getMessage(), 'No such price') !== false) {
                    // Price doesn't exist in current mode, set renewal fee to 0
                    debugging('Recurring price ' . $instance->customtext4 . ' not found in current Stripe mode.', DEBUG_DEVELOPER);
                    $renewal_fee = 0;
                } else {
                    throw $e;
                }
            } catch (Exception $e) {
                // If we can't get renewal fee, just use sign up fee
                $renewal_fee = 0;
            }
        }

        // Calculate subtotal (first billing cycle total) - this is key for matching Stripe
        $subtotal = $sign_up_fee + $renewal_fee;

        // Get coupon from Stripe
        \Stripe\Stripe::setApiKey($secretkey);

        try {
            // Try to retrieve coupon by ID directly
            $stripecoupon = null;
            $stripecoupon = \Stripe\Coupon::retrieve($couponid);

            if (!$stripecoupon) {
                throw new Exception('Coupon not found');
            }

            // Enhanced coupon validation
            if (!$stripecoupon || !$stripecoupon->valid) {
                throw new Exception('Invalid coupon');
            }

            // Check if coupon has expired
            if (isset($stripecoupon->redeem_by) && $stripecoupon->redeem_by < time()) {
                throw new Exception('Coupon has expired');
            }

            // Check if coupon has usage limits
            if (isset($stripecoupon->max_redemptions) && isset($stripecoupon->times_redeemed) &&
                $stripecoupon->times_redeemed >= $stripecoupon->max_redemptions) {
                throw new Exception('Coupon usage limit exceeded');
            }

            $couponname = isset($stripecoupon->name) ? $stripecoupon->name : $stripecoupon->id;
            $discountamount = 0;
            $coupontype = '';
            $discountvalue = 0;
            $recurring_discount_amount = 0;
            $discounted_renewal_fee = $renewal_fee;

            // Calculate discount based on subtotal
            if (isset($stripecoupon->percent_off)) {
                $discountamount = $subtotal * ($stripecoupon->percent_off / 100);
                $coupontype = 'percentoff';
                $discountvalue = $stripecoupon->percent_off;

                // For forever coupons, also apply discount to recurring fee
                if ($stripecoupon->duration === 'forever' && $renewal_fee > 0) {
                    $recurring_discount_amount = $renewal_fee * ($stripecoupon->percent_off / 100);
                    $discounted_renewal_fee = max(0, $renewal_fee - $recurring_discount_amount);
                }
            } else if (isset($stripecoupon->amount_off)) {
                // Ensure currency matches
                if (isset($stripecoupon->currency) && strtoupper($stripecoupon->currency) !== strtoupper($currency)) {
                    throw new Exception('Coupon currency does not match course currency');
                }
                // Stripe amount_off is in cents, convert to dollars and ensure it doesn't exceed subtotal
                $discountamount = min($stripecoupon->amount_off / 100, $subtotal);
                $coupontype = 'amountoff';
                $discountvalue = $stripecoupon->amount_off / 100;

                // For forever coupons, also apply discount to recurring fee
                if ($stripecoupon->duration === 'forever' && $renewal_fee > 0) {
                    $recurring_discount_amount = min($stripecoupon->amount_off / 100, $renewal_fee);
                    $discounted_renewal_fee = max(0, $renewal_fee - $recurring_discount_amount);
                }
            } else {
                throw new Exception('Invalid coupon type');
            }

            // Calculate final cost
            $finalcost = max(0, $subtotal - $discountamount);

            // Get duration information
            $couponduration = isset($stripecoupon->duration) ? $stripecoupon->duration : '';
            $coupondurationmonths = 0;
            if ($couponduration === 'repeating' && isset($stripecoupon->duration_in_months)) {
                $coupondurationmonths = $stripecoupon->duration_in_months;
            }

            // Add minimum cost validation after coupon application
            $minamount = [
                'USD' => 0.5, 'AED' => 2.0, 'AUD' => 0.5, 'BGN' => 1.0, 'BRL' => 0.5,
                'CAD' => 0.5, 'CHF' => 0.5, 'CZK' => 15.0, 'DKK' => 2.5, 'EUR' => 0.5,
                'GBP' => 0.3, 'HKD' => 4.0, 'HUF' => 175.0, 'INR' => 0.5, 'JPY' => 50,
                'MXN' => 10, 'MYR' => 2, 'NOK' => 3.0, 'NZD' => 0.5, 'PLN' => 2.0,
                'RON' => 2.0, 'SEK' => 3.0, 'SGD' => 0.5, 'THB' => 10,
            ];
            $minamount = isset($minamount[$currency]) ? $minamount[$currency] : 0.5;

            // Determine UI state based on final cost
            $uistate = 'discount'; // Default state when coupon is applied but payment still required
            $errormessage = '';

            if ($finalcost <= 0) {
                $uistate = 'paid'; // Course is completely free
            } else if ($finalcost < $minamount) {
                $uistate = 'error';
                $errormessage = "Amount $currency " . number_format($finalcost, 2) . " is below minimum $currency " . number_format($minamount, 2);
            }

            return [
                'status' => format_float($finalcost, 2, false),
                'couponname' => $couponname,
                'coupontype' => $coupontype,
                'discountvalue' => $discountvalue,
                'currency' => $currency,
                'discountamount' => format_float($discountamount, 2, false),
                'couponduration' => $couponduration,
                'coupondurationmonths' => $coupondurationmonths,
                'recurring_discount_amount' => format_float($recurring_discount_amount, 2, false),
                'discounted_renewal_fee' => format_float($discounted_renewal_fee, 2, false),
                'original_renewal_fee' => format_float($renewal_fee, 2, false),
                'uistate' => $uistate,
                'message' => $uistate === 'error' ? $errormessage : 'Coupon applied successfully.',
                'showsections' => [
                    'paidenrollment' => ($uistate === 'paid'),
                    'discountsection' => ($discountamount > 0),
                ],
            ];

        } catch (Exception $e) {
            throw new moodle_exception('invalidcoupon', 'enrol_stripepaymentpro', '', $e->getMessage());
        }
    }


	/**
	 * parameter declairation of stripe_enrol
	 */
    public static function stripe_enrol_parameters() {
        return new external_function_parameters(
            array(
                'userid' => new external_value(PARAM_RAW, 'Update data user id'),
                'couponid' => new external_value(PARAM_RAW, 'Update coupon id'),
                'instanceid' => new external_value(PARAM_RAW, 'Update instance id'),
            )
        );
    }
	
	/**
	 * declairation of return type of stripe_enrol
	 */
    public static function stripe_enrol_returns() {
        return new external_single_structure(
            [
                'status' => new external_value(PARAM_RAW, 'status: true if success or 0 if failure'),
                'redirecturl' => new external_value(PARAM_URL, 'Stripe Checkout URL', VALUE_OPTIONAL),
                'paymentintent' => new external_value(PARAM_RAW, 'JSON string containing client_secret and session_id', VALUE_OPTIONAL),
                'error' => new external_single_structure(
                [
                    'message' => new external_value(PARAM_TEXT, 'Error message', VALUE_OPTIONAL),
                ], VALUE_OPTIONAL
            )
            ]
        );
    }

	/**
	 * Enhanced enrol function with pro features
	 * Extends base stripepayment functionality with minimal additional code
	 * @param $userid number user id of the student
	 * @param $couponid value of the coupon id if applied coupon
	 * @param $instanceid instance id of the plugin instance
	 */
    public static function stripe_enrol($userid, $couponid, $instanceid) {
        global $CFG, $DB;

        // PRO FEATURE: Check if we need pro-specific handling
        $instance = $DB->get_record('enrol', ['id' => $instanceid, 'enrol' => 'stripepaymentpro'], '*', MUST_EXIST);
        $payment_gateway_type = get_config('enrol_stripepaymentpro', 'payment_gateway_type');
        $recurringpriceid = $instance->customtext4;
        $enable_automatic_tax = !empty(get_config('enrol_stripepaymentpro', 'enable_automatic_tax'));

        // If no pro features are configured, use base functionality
        if (empty($payment_gateway_type) && empty($recurringpriceid) && !$enable_automatic_tax) {
            return parent::stripepayment_enrol($userid, $couponid, $instanceid);
        }

        // PRO FEATURE: Enhanced coupon validation with product restriction
        if ($couponid) {
            $stripeproductid = $instance->customtext2 ?? null;
            $coupon = $DB->get_record('enrol_stripepro_coupons', ['couponid' => $couponid], '*', IGNORE_MISSING);
            if (!$coupon) {
                $coupon = $DB->get_record_sql("
                    SELECT *
                    FROM {enrol_stripepro_coupons}
                    WHERE LOWER(coupon_name) = LOWER(:coupon_name)
                    LIMIT 1
                ", ['coupon_name' => $couponid]);
            }

            if ($coupon && !empty($coupon->stripe_product_id) && $coupon->stripe_product_id !== $stripeproductid) {
                return [
                    'status' => 0,
                    'error' => ['message' => 'Coupon not valid for this product'],
                ];
            }
            if ($coupon) {
                $couponid = $coupon->couponid;
            }
        }

        // For pro features, we need custom session creation
        return self::create_pro_checkout_session($userid, $couponid, $instanceid);
    }

    /**
     * Create pro checkout session with advanced features
     * @param int $userid User ID
     * @param string $couponid Coupon ID
     * @param int $instanceid Instance ID
     * @return array Response with session URL
     */
    private static function create_pro_checkout_session($userid, $couponid, $instanceid) {
        global $CFG, $DB;

        // Validate data using base functionality
        $validateddata = self::validate_data($userid, $instanceid);
        $plugininstance = $validateddata[0];
        $course = $validateddata[1];
        $context = $validateddata[2];
        $user = $validateddata[3];

        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        $payment_gateway_type = get_config('enrol_stripepaymentpro', 'payment_gateway_type');

        Stripe::setApiKey($secretkey);

        // Get customer from pro table
        $checkcustomer = $DB->get_record('enrol_stripepaymentpro', ['receiver_email' => $user->email], '*', IGNORE_MISSING);
        $receiver_id = $checkcustomer ? $checkcustomer->receiver_id : null;

        // PRO FEATURE: Use Stripe Price objects instead of inline pricing
        try {
            $price = Price::retrieve($plugininstance->customtext3);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            if (strpos($e->getMessage(), 'No such price') !== false) {
                return [
                    'status' => 0,
                    'error' => ['message' => 'Price not found in current Stripe mode. Please contact administrator to recreate pricing.'],
                ];
            }
            throw $e;
        }

        $session_params = [
            'customer' => $receiver_id,
            'customer_email' => $receiver_id ? null : $user->email,
            'mode' => 'payment',
            'line_items' => [['price' => $price->id, 'quantity' => 1]],
            'metadata' => [
                'userid' => $userid,
                'couponid' => $couponid,
                'instanceid' => $instanceid,
            ]
        ];

        // PRO FEATURE: Subscription support
        if (!empty($plugininstance->customtext4)) {
            $session_params['mode'] = 'subscription';
            $session_params['line_items'][] = ['price' => $plugininstance->customtext4, 'quantity' => 1];

            // Trial period
            if ($plugininstance->customint2 > 0) {
                $session_params['subscription_data']['trial_end'] = time() + $plugininstance->customint2;
            }
        }

        // PRO FEATURE: Automatic tax
        if (!empty(get_config('enrol_stripepaymentpro', 'enable_automatic_tax'))) {
            $session_params['automatic_tax'] = ['enabled' => true];
            $session_params['billing_address_collection'] = 'required';
        }

        // PRO FEATURE: Coupon handling for subscriptions
        if ($couponid) {
            $session_params['discounts'] = [['coupon' => $couponid]];
        }

        // PRO FEATURE: Payment gateway type
        if ($payment_gateway_type === 'elements') {
            // For Elements mode, use Embedded Checkout (not PaymentIntent)
            // This supports both one-time payments and subscriptions
            $session_params['ui_mode'] = 'embedded';
            $session_params['return_url'] = $CFG->wwwroot . '/enrol/stripepaymentpro/thankyou.php?session_id={CHECKOUT_SESSION_ID}';

            try {
                $session = Session::create($session_params);

                return [
                    'status' => 'success',
                    'paymentintent' => json_encode([
                        'client_secret' => $session->client_secret
                    ]),
                    'error' => [],
                ];
            } catch (\Exception $e) {
                return [
                    'status' => 0,
                    'error' => ['message' => $e->getMessage()],
                ];
            }
        }
        else if ($payment_gateway_type == 'checkout') {
            // Add required success_url for checkout mode
            $session_params['success_url'] = $CFG->wwwroot . '/enrol/stripepaymentpro/thankyou.php?session_id={CHECKOUT_SESSION_ID}';
            $session_params['cancel_url'] = $CFG->wwwroot . '/course/view.php?id=' . $plugininstance->courseid;

            try {
                $session = Session::create($session_params);
                return [
                    'status' => 'success',
                    'redirecturl' => $session->url,
                    'error' => []
                ];
            } catch (Exception $e) {
                return [
                    'status' => 0,
                    'error' => ['message' => $e->getMessage()],
                ];
            }
        }
    }

    /**
     * Send enrollment notifications to students, teachers, and admins
     *
     * @param stdClass $course The course object
     * @param stdClass $context The course context
     * @param stdClass $user The enrolled user
     * @param object $plugin The enrollment plugin instance
     */
    private static function send_enrollment_notifications($course, $context, $user, $plugin) {
        global $DB, $CFG;

        // Get plugin configuration for notifications
        $mailstudents = $plugin->get_config('mailstudents');
        $mailteachers = $plugin->get_config('mailteachers'); 
        $mailadmins = $plugin->get_config('mailadmins');

        // Prepare order details for notifications
        $orderdetails = new stdClass();
        $orderdetails->coursename = format_string($course->fullname, true, ['context' => $context]);
        $orderdetails->username = fullname($user);
        $orderdetails->profileurl = "$CFG->wwwroot/user/view.php?id=$user->id";

        // Get teacher for notifications
        $teacher = get_users_by_capability($context, 'moodle/course:update', 'u.*', 'u.id ASC', '', '', '', '', false, true);
        $teacher = !empty($teacher) ? reset($teacher) : null;

        // Send notification to student
        if (!empty($mailstudents)) {
            $userfrom = empty($teacher) ? core_user::get_noreply_user() : $teacher;
            $fullmessage = get_string('welcometocoursetext', '', $orderdetails);
            $fullmessagehtml = '<p>'.get_string('welcometocoursetext', '', $orderdetails).'</p>';

            $message = new \core\message\message();
            $message->component = 'enrol_stripepaymentpro';
            $message->name = 'stripepaymentpro_enrolment';
            $message->userfrom = $userfrom;
            $message->userto = $user;
            $message->subject = get_string('enrolmentnew', 'enrol', $orderdetails->coursename);
            $message->fullmessage = $fullmessage;
            $message->fullmessageformat = FORMAT_PLAIN;
            $message->fullmessagehtml = $fullmessagehtml;
            $message->smallmessage = '';
            $message->notification = 1;

            $messageid = message_send($message);
            if (!$messageid) {
                debugging('Failed to send stripepaymentpro enrolment notification to student: ' . $user->id, DEBUG_DEVELOPER);
            }
        }

        // Send notification to teacher
        if (!empty($mailteachers) && !empty($teacher)) {
            $fullmessage = get_string('enrolmentnewuser', 'enrol', $orderdetails);
            $fullmessagehtml = '<p>'.get_string('enrolmentnewuser', 'enrol', $orderdetails).'</p>';

            $message = new \core\message\message();
            $message->component = 'enrol_stripepaymentpro';
            $message->name = 'stripepaymentpro_enrolment';
            $message->userfrom = core_user::get_noreply_user();
            $message->userto = $teacher;
            $message->subject = get_string('enrolmentnew', 'enrol', $orderdetails->coursename);
            $message->fullmessage = $fullmessage;
            $message->fullmessageformat = FORMAT_PLAIN;
            $message->fullmessagehtml = $fullmessagehtml;
            $message->smallmessage = '';
            $message->notification = 1;

            $messageid = message_send($message);
            if (!$messageid) {
                debugging('Failed to send stripepaymentpro enrolment notification to teacher: ' . $teacher->id, DEBUG_DEVELOPER);
            }
        }

        // Send notification to admins
        if (!empty($mailadmins)) {
            $admins = get_admins();
            foreach ($admins as $admin) {
                $fullmessage = get_string('enrolmentnewuser', 'enrol', $orderdetails);
                $fullmessagehtml = '<p>'.get_string('enrolmentnewuser', 'enrol', $orderdetails).'</p>';

                $message = new \core\message\message();
                $message->component = 'enrol_stripepaymentpro';
                $message->name = 'stripepaymentpro_enrolment';
                $message->userfrom = core_user::get_noreply_user();
                $message->userto = $admin;
                $message->subject = get_string('enrolmentnew', 'enrol', $orderdetails->coursename);
                $message->fullmessage = $fullmessage;
                $message->fullmessageformat = FORMAT_PLAIN;
                $message->fullmessagehtml = $fullmessagehtml;
                $message->smallmessage = '';
                $message->notification = 1;

                $messageid = message_send($message);
                if (!$messageid) {
                    debugging('Failed to send stripepaymentpro enrolment notification to admin: ' . $admin->id, DEBUG_DEVELOPER);
                }
            }
        }
    }

    /**
     * validate plugininstance, course, user, context if validate then ok
     * else send message to admin
     */
    public static function validate_data($userid, $instanceid) {
        global $DB, $CFG;

        // Convert parameters to proper types
        $userid = (int)$userid;
        $instanceid = (int)$instanceid;

        // Validate enrolment instance.
        if (!$plugininstance = $DB->get_record("enrol", ["id" => $instanceid, "status" => 0])) {
            self::message_stripepaymentpro_error_to_admin(get_string('invalidinstance', 'enrol_stripepaymentpro'), ["id" => $instanceid]);
            throw new moodle_exception('invalidinstance', 'enrol_stripepaymentpro');
        }

        // Validate course.
        if (!$course = $DB->get_record("course", ["id" => $plugininstance->courseid])) {
            self::message_stripepaymentpro_error_to_admin(get_string('invalidcourseid', 'enrol_stripepaymentpro'), ["id" => $plugininstance->courseid]);
            throw new moodle_exception('invalidcourseid', 'enrol_stripepaymentpro');
        }

        // Validate context.
        if (!$context = context_course::instance($course->id, IGNORE_MISSING)) {
            self::message_stripepaymentpro_error_to_admin(get_string('invalidcontextid', 'enrol_stripepaymentpro'), ["id" => $course->id]);
            throw new moodle_exception('invalidcontextid', 'enrol_stripepaymentpro');
        }

        // Validate user.
        if (!$user = $DB->get_record("user", ["id" => $userid])) {
            self::message_stripepaymentpro_error_to_admin("Not a valid user id", ["id" => $userid]);
            throw new moodle_exception('invaliduserid', 'enrol_stripepaymentpro');
        }
        return [$plugininstance, $course, $context, $user];
    }

	/**
	 * parametere defination deactivate_coupon
	 */
    public static function deactivate_coupon_parameters() {
        return new external_function_parameters(
            array(
                'courseid' => new external_value(PARAM_RAW, 'Course ID (Stripe Product ID)'),
                'couponid' => new external_value(PARAM_RAW, 'Coupon ID')
            )
        );
    }
	
	/**
	 * function for deactivate a single coupon from a single course
	 * @param $courseid number target course id (stripe product id) for which the coupon will be deactivate
	 * @param $couponid number coupon id which will be deactivate for specific coupon.
	 */
    public static function deactivate_coupon($courseid, $couponid){
        global $DB, $CFG;

        require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

        try {
            // Get the coupon record from database
            $coupon = $DB->get_record('enrol_stripepro_coupons', ['couponid' => $couponid]);
            if (!$coupon) {
                throw new Exception('Coupon not found in database');
            }

            // Initialize Stripe client
            $stripe_secret_key = get_config('enrol_stripepayment', 'secretkey');
            if (empty($stripe_secret_key)) {
                throw new Exception('Stripe secret key not configured');
            }

            $stripe = new \Stripe\StripeClient($stripe_secret_key);

            // Delete coupon from Stripe first
            try {
                $stripe->coupons->delete($couponid);
            } catch (\Stripe\Exception\InvalidRequestException $e) {
                // Coupon might already be deleted from Stripe, continue with database deletion
                error_log('Stripe coupon deletion failed (might already be deleted): ' . $e->getMessage());
            }

            // Delete from local database
            $DB->delete_records('enrol_stripepro_coupons', ['couponid' => $couponid]);

            return ['success' => true, 'message' => 'Coupon deleted successfully'];

        } catch (Exception $e) {
            error_log('Error deactivating coupon: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

	/**
	 * return type for deactivate_coupon
	 */
    public static function deactivate_coupon_returns() {
        return new external_single_structure([
            'success' => new external_value(PARAM_BOOL, 'True if coupon deleted successfully'),
            'message' => new external_value(PARAM_TEXT, 'Success or error message')
        ]);
    }


    /**
	 * parametere defination deactivate_all_coupons
	 */
    public static function deactivate_all_coupons_parameters() {
        return new external_function_parameters(
            array(
                'courseid' => new external_value(PARAM_TEXT, 'Course ID (Stripe Product ID)')
            )
        );
    }

	/**
	 * deactivate all coupon for that specific course.
	 * @param $courseid number for which course the all coupon should be de activated
	 */
    public static function deactivate_all_coupons( $courseid ) {
        global $DB, $CFG;

        require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

        try {
            // Initialize Stripe client
            $stripe_secret_key = get_config('enrol_stripepayment', 'secretkey');
            if (empty($stripe_secret_key)) {
                throw new Exception('Stripe secret key not configured');
            }

            $stripe = new \Stripe\StripeClient($stripe_secret_key);

            // Get all coupons for this course
            $coupons = $DB->get_records('enrol_stripepro_coupons', array('stripe_product_id' => $courseid));
            $deleted_count = 0;
            $errors = [];

            foreach ($coupons as $coupon) {
                try {
                    // Delete from Stripe first
                    try {
                        $stripe->coupons->delete($coupon->couponid);
                    } catch (\Stripe\Exception\InvalidRequestException $e) {
                        // Coupon might already be deleted from Stripe, continue
                        error_log('Stripe coupon deletion failed for ' . $coupon->couponid . ': ' . $e->getMessage());
                    }

                    // Delete from local database
                    $DB->delete_records('enrol_stripepro_coupons', array('couponid' => $coupon->couponid));
                    $deleted_count++;

                } catch (Exception $e) {
                    $errors[] = 'Failed to delete coupon ' . $coupon->couponid . ': ' . $e->getMessage();
                    error_log('Error deleting coupon ' . $coupon->couponid . ': ' . $e->getMessage());
                }
            }

            if (count($errors) > 0) {
                return ['success' => false, 'message' => 'Some coupons could not be deleted: ' . implode(', ', $errors)];
            }

            return ['success' => true, 'message' => "Successfully deleted {$deleted_count} coupon(s)"];

        } catch(Exception $e) {
            error_log('Error in deactivate_all_coupons: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

	/**
	 * return type of deactivate_all_coupons
	 */
    public static function deactivate_all_coupons_returns() {
        return new external_single_structure([
            'success' => new external_value(PARAM_BOOL, 'True if coupons deleted successfully'),
            'message' => new external_value(PARAM_TEXT, 'Success or error message')
        ]);
    }


	/**
	 * function for send any error to admn if any error occurs
	 * @param $subject subject of the error message 
	 */
    public static function message_stripepaymentpro_error_to_admin($subject, $data) {
        $admin = get_admin();
        $site = get_site();
    	 $dataString = '';
    	if (is_array($data)) {
        foreach ($data as $key => $value) {
            $dataString .= $key . ": " . $value . "\n";
        }
    }
        $message = "$site->fullname: Transaction failed.\n\n$subject\n\n$dataString\n\n";
        $subject = "STRIPE PAYMENT ERROR: ".$subject;
        $fullmessage = $message;
        $fullmessagehtml = html_to_text('<p>'.$message.'</p>');
        // Send test email.
        ob_start();
        email_to_user($admin, $admin, $subject, $fullmessage, $fullmessagehtml);
        ob_get_contents();
        ob_end_clean();
    }


	/**
	 * parameter defination for  webhook_handler
	 * its empty because event can be many types so the parameter can be different type 
	 */ 
	public static function webhook_handler_parameters() {
		return new external_function_parameters([
        	
        ]);
	}

	/**
	 * function of listinig to the events comes from stripe
	 */
	public static function webhook_handler() {
    	$payload = @file_get_contents('php://input');
		$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
		
    	//getting the webhook secret from config which was set in the time of activating license
    	$endpoint_secret = get_config('enrol_stripepaymentpro', 'stripe_webhook_secret');
    	
    	try {
        	$event = \Stripe\Webhook::constructEvent(
    			$payload, $sig_header, $endpoint_secret
  			);
        }catch(Exception $e) {
        	exit();
        }
    	$web_hook_helper = new webhook_helper($event->data->object->id);

        switch($event->type){
                 	//if the check out has sucessfully paid then enrol the user to the course
        	case 'checkout.session.completed':
        		$web_hook_helper->successfull_enrolment();
        		break;
        	// if the subscription is deleted then check the  end date and if necessery unenrol the user
            case 'customer.subscription.deleted':
				$web_hook_helper->unenrol_user_on_subscription_deleted($event->data->object->id);
        		break;
    	}
    	
		return true;
	}

	/**
	 * return type for webhook_handler
	 */
	public static function webhook_handler_returns() {
		return new external_value(PARAM_RAW, 'status', 999 );
	}
}
