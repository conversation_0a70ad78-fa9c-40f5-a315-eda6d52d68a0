{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol/enrolment_options

    This template will render information about available course enrolment options.

    Context variables required for this template:
    * courseinfobox - Course information box
    * widgets - array of widgets to display
    * message - Error message and continue button

    Example context (json):
    {
        "courseinfobox": "<div>Course Information</div>",
        "widgets": [],
        "message": "Guests cannot access this course. Please log in.",
        "continuebutton": "<a href=\"#\"><span class=\"instancename\">Continue</span></a>"
    }

}}
<h2>{{heading}}</h2>
{{{courseinfobox}}}
{{#widgets}}{{{.}}}{{/widgets}}
{{^widgets}}
    <div id="notice" class="box py-3 generalbox">
        {{message}}
    </div>
    {{{continuebutton}}}
{{/widgets}}
