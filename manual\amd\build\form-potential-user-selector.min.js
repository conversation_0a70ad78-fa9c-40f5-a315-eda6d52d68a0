/**
 * Potential user selector module.
 *
 * @module     enrol_manual/form-potential-user-selector
 * @copyright  2016 Damyon Wiese
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("enrol_manual/form-potential-user-selector",["jquery","core/ajax","core/templates","core/str"],(function($,Ajax,Templates,Str){return{processResults:function(selector,results){var users=[];return $.isArray(results)?($.each(results,(function(index,user){users.push({value:user.id,label:user._label})})),users):results},transport:function(selector,query,success,failure){var courseid=$(selector).attr("courseid"),userfields=$(selector).attr("userfields").split(",");void 0===courseid&&(courseid="1");var enrolid=$(selector).attr("enrolid");void 0===enrolid&&(enrolid="");var perpage=parseInt($(selector).attr("perpage"));isNaN(perpage)&&(perpage=100),Ajax.call([{methodname:"core_enrol_get_potential_users",args:{courseid:courseid,enrolid:enrolid,search:query,searchanywhere:!0,page:0,perpage:perpage+1}}])[0].then((function(results){var promises=[],i=0;if(results.length<=perpage){const profileRegex=/^profile_field_(.*)$/;return $.each(results,(function(index,user){var ctx=user,identity=[];$.each(userfields,(function(i,k){const result=profileRegex.exec(k);result?user.customfields&&user.customfields.forEach((function(customfield){customfield.shortname===result[1]&&(ctx.hasidentity=!0,identity.push(customfield.value))})):void 0!==user[k]&&""!==user[k]&&(ctx.hasidentity=!0,identity.push(user[k]))})),ctx.identity=identity.join(", "),promises.push(Templates.render("enrol_manual/form-user-selector-suggestion",ctx))})),$.when.apply($.when,promises).then((function(){var args=arguments;$.each(results,(function(index,user){user._label=args[i],i++})),success(results)}))}return Str.get_string("toomanyuserstoshow","core",">"+perpage).then((function(toomanyuserstoshow){success(toomanyuserstoshow)}))})).fail(failure)}}}));

//# sourceMappingURL=form-potential-user-selector.min.js.map