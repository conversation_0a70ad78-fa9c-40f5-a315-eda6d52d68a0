Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF863190000 ntdll.dll
7FF861390000 KERNEL32.DLL
7FF860280000 KERNELBASE.dll
7FF861D50000 USER32.dll
7FF860A60000 win32u.dll
7FF8612E0000 GDI32.dll
7FF860BD0000 gdi32full.dll
7FF8609C0000 msvcp_win.dll
7FF860D00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF860E20000 advapi32.dll
7FF862110000 msvcrt.dll
7FF8617F0000 sechost.dll
7FF8607D0000 bcrypt.dll
7FF861FF0000 RPCRT4.dll
7FF85FB00000 CRYPTBASE.DLL
7FF860940000 bcryptPrimitives.dll
7FF861A30000 IMM32.DLL
