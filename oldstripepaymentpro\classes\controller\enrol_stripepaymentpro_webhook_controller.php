<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');
/**
 * controller for webhook settings
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_stripepaymentpro_webhook_controller {

	/**
	 * stripe client for comunication with stripe
	 */
    private $stripeClient;

	/**
	 * instance of stripe payment pro plugin
	 */ 
    private $plugin;
	
	/**
	 * instance of stripe payment plugin
	 */ 
    private $plugincore;

	/**
	 * constructor for class 
	 */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepayment');
        try {
            $this->stripeClient  = new \Stripe\StripeClient(get_config('enrol_stripepayment', 'secretkey'));
        } catch (\Exception $e){
            \core\notification::error($e->getMessage()); 
        }  
    }
	
	/**
	 * funtion for create webhook endpont and connect with our application
	 */
    public function create_webhook() {
        global $CFG;
    	$usertoken = get_config('enrol_stripepaymentpro', 'webservice_token');
        try {
            $webhook = $this->stripeClient->webhookEndpoints->create([
                'enabled_events' => [
                    'checkout.session.completed',
                    'checkout.session.async_payment_failed',
                    'customer.subscription.deleted',
                    'customer.subscription.updated',
            	],
                'url' => $CFG->wwwroot . '/webservice/rest/server.php?wstoken='.$usertoken.'&wsfunction=moodle_stripepaymentpro_webhook_handler',
            ]);

            // Save the webhook ID for future use (deletion, verification, etc.)
            set_config('stripe_webhook_id', $webhook->id, 'enrol_stripepaymentpro');
            set_config('stripe_webhook_secret', $webhook->secret, 'enrol_stripepaymentpro');
        } catch ( \Exception $e ) {
            \core\notification::error($e->getMessage());
        }
    }
	
	/**
	 * function for delete webhook if the license is deactive
	 * 
	 * @param $webhookid string id of the webhook connected to our application
	 */
    public function delete_webhook($webhookid) {
        global $CFG;
      

        if ( !$webhookid ) {
            $error = 'No webhook ID found in configuration';
            \core\notification::error($error);
            return;
        }

        try {
            $this->stripeClient->webhookEndpoints->delete($webhookid, []);
            // Set the webhook ID configuration to null
            set_config('stripe_webhook_id', null, 'enrol_stripepaymentpro');
        } catch (\Exception $e) {
            echo 'Error deleting webhook endpoint: ' . $e->getMessage();
        }
    }
}
