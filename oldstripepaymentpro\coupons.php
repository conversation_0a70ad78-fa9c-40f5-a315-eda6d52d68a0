<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
require_once('../../config.php');
require_once('../locallib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');
require_once('coupon_form.php');

use \Stripe\StripeClient;

global $CFG, $DB, $PAGE, $USER;

require_login();

// Check if the user is an admin
if (!is_siteadmin($USER->id)) {
    redirect($CFG->wwwroot);
}

// Set up page
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Stripe Coupon Management');
$PAGE->set_heading('Stripe Coupon Management');
$PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/coupons.php');
$PAGE->set_pagelayout('admin');

$plugin = enrol_get_plugin('stripepaymentpro');
$plugincore = enrol_get_plugin('stripepayment');

$return = new moodle_url('/enrol/stripepaymentpro/coupons.php');

echo $OUTPUT->header();
?>
<!-- button for switching tabs-->
<nav class='strip-coupon-navigation-section'>
	<button class='active' id="all_coupon_button"><?php echo get_string('all_coupons', 'enrol_stripepaymentpro'); ?></button>
	<button class='' id="generate_coupons_button"><?php echo get_string('generate_coupons', 'enrol_stripepaymentpro'); ?></button>
</nav>

<!-- Display table of courses and associated coupons-->
<section class='all-coupons-section' id='all_coupons_section'>
<?php
// Fetch coupons
$coupons = $DB->get_records('enrol_stripepro_coupons');

// Group coupons by course
$coupons_by_course = $coupons_list = $courses = [];
foreach ($coupons as $record) {
    $coupons_by_course[$record->stripe_product_id][] = $record->couponid;
    $coupons_list[$record->couponid] = $record;
}

// Fetch course names
$stripe_product_ids = array_keys($coupons_by_course);

if(count($stripe_product_ids) > 0) {
    list($insql, $inparams) = $DB->get_in_or_equal($stripe_product_ids);
    $query = "SELECT e.customtext2, c.id, c.fullname
                FROM {course} as c 
                JOIN {enrol} as e 
                  ON c.id = e.courseid
               WHERE e.customtext2 $insql";
    $courses = $stripe_product_ids ? $DB->get_records_sql($query, $inparams) : [];
    $courses[0] = (object) ["customtext2" => 0, "id" => 0, "fullname" => "All Courses"]; // Adding name for All courses
}
if (!empty($coupons_by_course)): ?>
    <table>
        <thead>
            <tr>
                <th>Course Name</th>
                <th>Coupons</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($coupons_by_course as $course_id => $coupons): ?>
                <tr>
                    <td><?php echo htmlspecialchars($courses[$course_id]->fullname); ?></td>
                    <td class='coupon-list'>
                        <?php 
                        foreach ($coupons as $coupon_id):
                            echo "<span class='coupon-name'>";
                                echo htmlspecialchars($coupons_list[$coupon_id]->coupon_name);
                                if ($coupons_list[$coupon_id]->coupon_name):
                                    echo " (";
                                    echo $coupons_list[$coupon_id]->percent_off > 0 
                                            ? $coupons_list[$coupon_id]->percent_off . '% off' 
                                            : (isset($coupons_list[$coupon_id]->amount_off)
                                                ? html_entity_decode($plugincore->show_currency_symbol(strtolower($coupons_list[$coupon_id]->currency))) . "" . ($coupons_list[$coupon_id]->amount_off / $plugin->get_fractional_unit_amount(strtoupper($coupons_list[$coupon_id]->currency)))
                                            : '') . " off";
                                    echo ($coupons_list[$coupon_id]->duration == "repeating") ? " for " . $coupons_list[$coupon_id]->no_of_months . " months" : " " . $coupons_list[$coupon_id]->duration;
                                    echo $coupons_list[$coupon_id]->coupon_expiry > 0 ? ". Expiry: ". userdate($coupons_list[$coupon_id]->coupon_expiry) : ". Expiry: Never";
                                    echo ")";
                                endif;
                                echo '<button class="deactivate-coupon" data-stripe_product_id="' . $courses[$course_id]->customtext2 . '" data-couponid="' . $coupon_id . '"><i class="fa-solid fa-trash"></i></button>';
                            echo '</span>';
                        endforeach;
                        ?>
                    </td>
                    <td>
                        <?php if (count($coupons) > 1): ?>
                            <button class="deactivate-all-coupons" data-stripe_product_id="<?php echo $courses[$course_id]->customtext2; ?>"><?php echo get_string('deactivate_all_coupons', 'enrol_stripepaymentpro'); ?><i class="fa-solid fa-trash"></i></button>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php else: ?>
    <p><?php echo get_string('no_coupon_found', 'enrol_stripepaymentpro'); ?></p>
<?php endif; ?>
</section>         
<section class='generate-coupon-section' id='generate_coupon_section'>
    <!-- form for generate coupon -->
    <?php
    // Fetch courses with stripepaymentpro enrolment method
    $courses_with_stripe = $DB->get_records_sql(
        "SELECT DISTINCT e.customtext2, c.fullname
                    FROM {enrol} e
                    JOIN {course} c ON e.courseid = c.id
                   WHERE e.enrol = 'stripepaymentpro'"
    );
    $stripe_course_list = [];
    foreach ($courses_with_stripe as $course) {
        $stripe_course_list[$course->customtext2] = $course->fullname;
    }
    
    $mform = new enrol_stripepaymentpro_coupon_form(null, array($stripe_course_list, $plugin, $plugincore));

    // Form processing and displaying is done here.
    if ($mform->is_cancelled()) {
        // If there is a cancel element on the form, and it was pressed,
        // then the `is_cancelled()` function will return true.
        // You can handle the cancel operation here.
        redirect($return);
    } else if ($formdata = $mform->get_data()) {
        $stripe_create_coupon_params = [];
        
        $stripe_create_coupon_params['name'] = $formdata->coupon_name;
    
        if ($formdata->coupon_expiry > time()) {
            $stripe_create_coupon_params['redeem_by'] = $formdata->coupon_expiry;
        }

       
        if ($formdata->coupon_types == "amount_off") {
            $stripe_create_coupon_params['amount_off'] = intval($formdata->discount_amount) * $plugin->get_fractional_unit_amount($formdata->coupon_currency);
            $stripe_create_coupon_params['currency'] = $formdata->coupon_currency;
        } else if ($formdata->coupon_types == "percent_off") {
            $stripe_create_coupon_params['percent_off'] = $formdata->discount_amount;
        }

        $stripe_create_coupon_params['duration'] = $formdata->coupon_duration;

        if ($formdata->coupon_duration == "repeating") {
            $stripe_create_coupon_params['duration_in_months'] = $formdata->coupon_duration_multiple_months_val;
        }

        if ($formdata->coupon_course_assignment != 0) {
            $stripe_create_coupon_params['applies_to']['products'] = [$formdata->coupon_course_assignment];
        }

        try {
            $stripe = new StripeClient(get_config('enrol_stripepayment', 'secretkey'));
            $coupon = $stripe->coupons->create($stripe_create_coupon_params);
            if($coupon != null && $coupon->id != "") {
                $record = new stdClass();
                $record->couponid = $coupon->id;
                $record->coupon_name = $coupon->name;
                $record->amount_off = $coupon->amount_off;
                $record->percent_off = $coupon->percent_off;
                $record->currency = $coupon->currency;
                $record->duration = $coupon->duration;
                $record->no_of_months = $coupon->duration_in_months ? $coupon->duration_in_months : 0;
                $record->stripe_product_id = $formdata->coupon_course_assignment;
                $record->timecreated = $coupon->created;
                $record->coupon_expiry = $coupon->redeem_by ? $coupon->redeem_by : 0;

                if (!$DB->record_exists('enrol_stripepro_coupons', ['couponid' => $record->couponid])) {
                    $DB->insert_record('enrol_stripepro_coupons', $record);
                } else {
                    \core\notification::error(get_string('duplicatedata', 'enrol_stripepaymentpro'));
                }
                echo "Coupon " . $coupon->name . " created successfully";
            }
        } catch (Exception $e) {
            \core\notification::error($e->getMessage());
        }
        redirect($return);
    } else {
        // This branch is executed if the form is submitted but the data doesn't
        // validate and the form should be redisplayed or on the first display of the form.

        // Set anydefault data (if any).
        $mform->set_data([]);

        // Display the form.
        $mform->display();
    }
    ?>
</section>
<?php
// Initialize JavaScript
$PAGE->requires->js_call_amd('enrol_stripepaymentpro/stripe_payment_pro', 'initCouponSettings', []);
echo $OUTPUT->footer();