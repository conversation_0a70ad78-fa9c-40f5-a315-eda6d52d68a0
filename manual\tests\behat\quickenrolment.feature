@enrol @enrol_manual
Feature: Teacher can search and enrol users one by one into the course
  In order to quickly enrol particular students into my course
  As a teacher
  I can search for the students and enrol them into the course

  Background:
    Given the following "custom profile fields" exist:
      | datatype | shortname  | name           |
      | text     | customid   | Custom user id |
    And the following "users" exist:
      | username    | firstname | lastname | email                   | profile_field_customid |
      | teacher001  | Teacher   | 001      | <EMAIL>  |                        |
      | student001  | Student   | 001      | <EMAIL>  | Q994                   |
      | student002  | Student   | 002      | <EMAIL>  | Q008                   |
      | student003  | Student   | 003      | <EMAIL>  | Z442                   |
      | student004  | Student   | 004      | <EMAIL>  |                        |
      | student005  | Student   | 005      | <EMAIL>  |                        |
      | student006  | Student   | 006      | <EMAIL>  |                        |
      | student007  | Student   | 007      | <EMAIL>  |                        |
      | student008  | Student   | 008      | <EMAIL>  |                        |
      | student009  | Student   | 009      | <EMAIL>  |                        |
      | student010  | Student   | 010      | <EMAIL>  |                        |
      | student011  | Student   | 011      | <EMAIL>  |                        |
      | student012  | Student   | 012      | <EMAIL>  |                        |
      | student013  | Student   | 013      | <EMAIL>  |                        |
      | student014  | Student   | 014      | <EMAIL>  |                        |
      | student015  | Student   | 015      | <EMAIL>  |                        |
      | student016  | Student   | 016      | <EMAIL>  |                        |
      | student017  | Student   | 017      | <EMAIL>  |                        |
      | student018  | Student   | 018      | <EMAIL>  |                        |
      | student019  | Student   | 019      | <EMAIL>  |                        |
      | student020  | Student   | 020      | <EMAIL>  |                        |
      | student021  | Student   | 021      | <EMAIL>  |                        |
      | student022  | Student   | 022      | <EMAIL>  |                        |
      | student023  | Student   | 023      | <EMAIL>  |                        |
      | student024  | Student   | 024      | <EMAIL>  |                        |
      | student025  | Student   | 025      | <EMAIL>  |                        |
      | student026  | Student   | 026      | <EMAIL>  |                        |
      | student027  | Student   | 027      | <EMAIL>  |                        |
      | student028  | Student   | 028      | <EMAIL>  |                        |
      | student029  | Student   | 029      | <EMAIL>  |                        |
      | student030  | Student   | 030      | <EMAIL>  |                        |
      | student031  | Student   | 031      | <EMAIL>  |                        |
      | student032  | Student   | 032      | <EMAIL>  |                        |
      | student033  | Student   | 033      | <EMAIL>  |                        |
      | student034  | Student   | 034      | <EMAIL>  |                        |
      | student035  | Student   | 035      | <EMAIL>  |                        |
      | student036  | Student   | 036      | <EMAIL>  |                        |
      | student037  | Student   | 037      | <EMAIL>  |                        |
      | student038  | Student   | 038      | <EMAIL>  |                        |
      | student039  | Student   | 039      | <EMAIL>  |                        |
      | student040  | Student   | 040      | <EMAIL>  |                        |
      | student041  | Student   | 041      | <EMAIL>  |                        |
      | student042  | Student   | 042      | <EMAIL>  |                        |
      | student043  | Student   | 043      | <EMAIL>  |                        |
      | student044  | Student   | 044      | <EMAIL>  |                        |
      | student045  | Student   | 045      | <EMAIL>  |                        |
      | student046  | Student   | 046      | <EMAIL>  |                        |
      | student047  | Student   | 047      | <EMAIL>  |                        |
      | student048  | Student   | 048      | <EMAIL>  |                        |
      | student049  | Student   | 049      | <EMAIL>  |                        |
      | student050  | Student   | 050      | <EMAIL>  |                        |
      | student051  | Student   | 051      | <EMAIL>  |                        |
      | student052  | Student   | 052      | <EMAIL>  |                        |
      | student053  | Student   | 053      | <EMAIL>  |                        |
      | student054  | Student   | 054      | <EMAIL>  |                        |
      | student055  | Student   | 055      | <EMAIL>  |                        |
      | student056  | Student   | 056      | <EMAIL>  |                        |
      | student057  | Student   | 057      | <EMAIL>  |                        |
      | student058  | Student   | 058      | <EMAIL>  |                        |
      | student059  | Student   | 059      | <EMAIL>  |                        |
      | student060  | Student   | 060      | <EMAIL>  |                        |
      | student061  | Student   | 061      | <EMAIL>  |                        |
      | student062  | Student   | 062      | <EMAIL>  |                        |
      | student063  | Student   | 063      | <EMAIL>  |                        |
      | student064  | Student   | 064      | <EMAIL>  |                        |
      | student065  | Student   | 065      | <EMAIL>  |                        |
      | student066  | Student   | 066      | <EMAIL>  |                        |
      | student067  | Student   | 067      | <EMAIL>  |                        |
      | student068  | Student   | 068      | <EMAIL>  |                        |
      | student069  | Student   | 069      | <EMAIL>  |                        |
      | student070  | Student   | 070      | <EMAIL>  |                        |
      | student071  | Student   | 071      | <EMAIL>  |                        |
      | student072  | Student   | 072      | <EMAIL>  |                        |
      | student073  | Student   | 073      | <EMAIL>  |                        |
      | student074  | Student   | 074      | <EMAIL>  |                        |
      | student075  | Student   | 075      | <EMAIL>  |                        |
      | student076  | Student   | 076      | <EMAIL>  |                        |
      | student077  | Student   | 077      | <EMAIL>  |                        |
      | student078  | Student   | 078      | <EMAIL>  |                        |
      | student079  | Student   | 079      | <EMAIL>  |                        |
      | student080  | Student   | 080      | <EMAIL>  |                        |
      | student081  | Student   | 081      | <EMAIL>  |                        |
      | student082  | Student   | 082      | <EMAIL>  |                        |
      | student083  | Student   | 083      | <EMAIL>  |                        |
      | student084  | Student   | 084      | <EMAIL>  |                        |
      | student085  | Student   | 085      | <EMAIL>  |                        |
      | student086  | Student   | 086      | <EMAIL>  |                        |
      | student087  | Student   | 087      | <EMAIL>  |                        |
      | student088  | Student   | 088      | <EMAIL>  |                        |
      | student089  | Student   | 089      | <EMAIL>  |                        |
      | student090  | Student   | 090      | <EMAIL>  |                        |
      | student091  | Student   | 091      | <EMAIL>  |                        |
      | student092  | Student   | 092      | <EMAIL>  |                        |
      | student093  | Student   | 093      | <EMAIL>  |                        |
      | student094  | Student   | 094      | <EMAIL>  |                        |
      | student095  | Student   | 095      | <EMAIL>  |                        |
      | student096  | Student   | 096      | <EMAIL>  |                        |
      | student097  | Student   | 097      | <EMAIL>  |                        |
      | student098  | Student   | 098      | <EMAIL>  |                        |
      | student099  | Student   | 099      | <EMAIL>  |                        |
    And the following "courses" exist:
      | fullname   | shortname | format | startdate       |
      | Course 001 | C001      | weeks  | ##1 month ago## |
    And the following "course enrolments" exist:
      | user       | course | role           | timestart       |
      | teacher001 | C001   | editingteacher | ##1 month ago## |
    And I log in as "teacher001"
    And I am on "Course 001" course homepage

  @javascript
  Scenario: Teacher can search and enrol one particular student
    Given I navigate to course participants
    And I press "Enrol users"
    When I set the field "Select users" to "student001"
    And I should see "Student 001"
    And I click on "Enrol users" "button" in the "Enrol users" "dialogue"
    Then I should see "Active" in the "Student 001" "table_row"
    And I should see "1 enrolled users"

  @javascript
  Scenario: Searching for a non-existing user
    Given I navigate to course participants
    And I press "Enrol users"
    And I click on "Select users" "field"
    And I type "qwertyuiop"
    Then I should see "No suggestions"

  @javascript
  Scenario: If there are less than 100 matching users, all are displayed for selection
    Given I navigate to course participants
    And I press "Enrol users"
    When I click on "Select users" "field"
    And I type "example.com"
    Then "Student 099" "autocomplete_suggestions" should exist

  @javascript
  Scenario: If there are more than 100 matching users, inform there are too many.
    Given the following "users" exist:
      | username    | firstname | lastname | email                   |
      | student100  | Student   | 100      | <EMAIL>  |
      | student101  | Student   | 101      | <EMAIL>  |
    And I navigate to course participants
    And I press "Enrol users"
    When I click on "Select users" "field"
    And I type "example.com"
    Then I should see "Too many users (>100) to show"

  @javascript
  Scenario: Changing the Maximum users per page setting affects the enrolment pop-up.
    Given the following config values are set as admin:
      | maxusersperpage | 5 |
    And I navigate to course participants
    And I press "Enrol users"
    When I click on "Select users" "field"
    And I type "student00"
    Then I should see "Too many users (>5) to show"

  @javascript
  Scenario: Change the Show user identity setting affects the enrolment pop-up.
    Given I log out
    When I log in as "admin"
    Then the following "users" exist:
      | username    | firstname | lastname | email                   | phone1     | phone2     | department | institution | city    | country  |
      | student100  | Student   | 100      | <EMAIL>  | 1234567892 | 1234567893 | ABC1       | ABC2        | CITY1   | GB       |
    And the following config values are set as admin:
      | showuseridentity | idnumber,email,city,country,phone1,phone2,department,institution |
    When I am on "Course 001" course homepage
    Then I navigate to course participants
    And I press "Enrol users"
    And I click on "Select users" "field"
    And I type "<EMAIL>"
    Then I should see "<EMAIL>, CITY1, GB, 1234567892, 1234567893, ABC1, ABC2"
    # Remove identity field in setting User policies
    And the following config values are set as admin:
      | showuseridentity | idnumber,email,phone1,phone2,department,institution |
    And I am on "Course 001" course homepage
    And I navigate to course participants
    And I press "Enrol users"
    And I click on "Select users" "field"
    And I type "<EMAIL>"
    And I should see "<EMAIL>, 1234567892, 1234567893, ABC1, ABC2"

  @javascript
  Scenario: Custom user profile fields work for search and display, if user has permission
    Given the following config values are set as admin:
      | showuseridentity | email,profile_field_customid |
    And I navigate to course participants
    And I press "Enrol users"
    When I set the field "Select users" to "Q994"
    Then I should see "<EMAIL>, Q994"
    And I click on "Cancel" "button" in the "Enrol users" "dialogue"
    And the following "permission overrides" exist:
      | capability                   | permission | role           | contextlevel | reference |
      | moodle/site:viewuseridentity | Prevent    | editingteacher | Course       | C001      |
    And I press "Enrol users"
    # Do this by keyboard because the 'I set the field' step doesn't let you set it to a missing value.
    And I press tab
    And I press tab
    And I press tab
    And I type "Q994"
    And I should see "No suggestions"

# The following tests are commented out as a result of MDL-66339.
#  @javascript
#  Scenario: Enrol user from participants page
#    Given I navigate to course participants
#    # Enrol user to course
#    And I press "Enrol users"
#    And I set the field "Select users" to "example.com"
#    And I expand the "Select users" autocomplete
#    When I click on "Student 099" item in the autocomplete list
#    Then I should see "Student 099" in the list of options for the "Select users" autocomplete
#    And I click on "Show more" "button"
#    # Fill data to input duration
#    And "input[name='timeend[enabled]'][checked=checked]" "css_element" should not exist
#    And the "Enrolment duration" "select" should be enabled
#    And I set the field "duration" to "2"
#    # Fill data to input end time
#    And I set the field "Starting from" to "2"
#    And I set the field "timeend[enabled]" to "1"
#    And I set the field "timeend[day]" to "10"
#    And the "Enrolment duration" "select" should be disabled
#    And I click on "Enrol users" "button" in the "Enrol users" "dialogue"
#    And I am on "Course 001" course homepage
#    And I navigate to course participants
#    And I should see "Student 099" in the "participants" "table"
#    And I click on "Edit enrolment" "icon" in the "Student 099" "table_row"
#    And the field "timeend[day]" matches value "10"
#
#  @javascript
#  Scenario: Update Enrol user
#    Given I am on "Course 001" course homepage
#    And I navigate to course participants
#    When I click on "Edit enrolment" "icon" in the "Teacher 001" "table_row"
#    Then the "Enrolment duration" "select" should be enabled
#    # Fill duration
#    And "input[name='timeend[enabled]'][checked=checked]" "css_element" should not exist
#    And the "Enrolment duration" "select" should be enabled
#    And I set the field "duration" to "2"
#    # Fill end time
#    And I set the field "timeend[enabled]" to "1"
#    And I set the field "timeend[day]" to "28"
#    And the "Enrolment duration" "select" should be disabled
#    And I press "Save changes"
#    And I click on "Edit enrolment" "icon" in the "Teacher 001" "table_row"
#    And the field "timeend[day]" matches value "28"
