{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/mysubscription

    Template for displaying user subscriptions

    Context variables required for this template:
    * subscriptions - Array of subscription data
    * is_admin - <PERSON>olean indicating if user is admin

    Example context (json):
    {
        "subscriptions": [
            {
                "subscription_id": "sub_123",
                "course_name": "Course Name",
                "username": "user123",
                "status": "active",
                "start_date": "2023-01-01 00:00:00",
                "next_payment": "2023-02-01 00:00:00",
                "is_active": true,
                "can_cancel": true
            }
        ],
        "is_admin": true,
        "has_subscriptions": true
    }
}}

<div class="container">
    {{#has_subscriptions}}
    <table class="table table-striped">
        <thead>
            <tr>
                <th>{{#str}}coursename, enrol_stripepaymentpro{{/str}}</th>
                {{#is_admin}}
                <th>{{#str}}subid, enrol_stripepaymentpro{{/str}}</th>
                <th>{{#str}}username, enrol_stripepaymentpro{{/str}}</th>
                {{/is_admin}}
                <th>{{#str}}status, enrol_stripepaymentpro{{/str}}</th>
                <th>{{#str}}startdate, enrol_stripepaymentpro{{/str}}</th>
                <th>{{#str}}nextpayment, enrol_stripepaymentpro{{/str}}</th>
                <th>{{#str}}action, enrol_stripepaymentpro{{/str}}</th>
            </tr>
        </thead>
        <tbody>
            {{#subscriptions}}
            <tr>
                <td>{{course_name}}</td>
                {{#is_admin}}
                <td>{{subscription_id}}</td>
                <td>{{username}}</td>
                {{/is_admin}}
                <td>{{status}}</td>
                <td>{{start_date}}</td>
                <td>{{next_payment}}</td>
                <td>
                    {{#can_cancel}}
                    <a class="btn btn-secondary"
                       onclick="return confirm('{{#str}}areyousure, enrol_stripepaymentpro{{/str}}\n{{#str}}areyousure_des, enrol_stripepaymentpro{{/str}}')"
                       href="?subid={{subscription_id}}">
                        {{#str}}cancelsubcription, enrol_stripepaymentpro{{/str}}
                    </a>
                    {{/can_cancel}}
                    {{^can_cancel}}
                    {{#str}}notactive, enrol_stripepaymentpro{{/str}}
                    {{/can_cancel}}
                </td>
            </tr>
            {{/subscriptions}}
        </tbody>
    </table>
    {{/has_subscriptions}}
    {{^has_subscriptions}}
    <p>{{#str}}no_subscriptions_found, enrol_stripepaymentpro{{/str}}</p>
    {{/has_subscriptions}}
</div>