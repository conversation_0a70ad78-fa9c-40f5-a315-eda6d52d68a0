<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.


/**
 * Stripe enrolment plugin.
 *
 * show the subscription to both admin and users
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
require_once('../../config.php');
require_once('../locallib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');

use \Stripe\StripeClient;

global $CFG, $DB, $PAGE, $USER;
require_login();
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Stripe Subscription Status');
$PAGE->set_heading('Stripe Subscription Status');
$PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php');
$plugin = enrol_get_plugin('stripepayment');

$stripe = new StripeClient(get_config('enrol_stripepayment', 'secretkey'));

// Fetch subscriptions for all user if the user is an admin.
if (is_siteadmin($USER->id)) {
    $subscriptions = $DB->get_records('enrol_stripepaymentpro', array('product_type' => 'service'));
} else {
	//fetch all subscription for the user.
    $subscriptions = $DB->get_records('enrol_stripepaymentpro', array('userid' => $USER->id, 'product_type' => 'service'));
}

// store all subscription objects in an array so that later we can get all the data.
$stripe_subscriptions = [];

// uses to store all data of user using subcriptioin id as a index
$subscriptions_by_id = [];

foreach ($subscriptions as $sub) {
	//  store all data of user using subcriptioin id as a index
	$subscriptions_by_id[$sub->subscription_id] = $sub;
    try {
        $subscription = $stripe->subscriptions->retrieve($sub->subscription_id);
        $stripe_subscriptions[] = $subscription;
    } catch (\Exception $e) {
        \core\notification::error($e->getMessage());
    }
}

// if the subid is there means there is an request for subuscription off for the specific subscription
$subid = optional_param('subid', null, PARAM_RAW);

if ($subid !== null) {
    $subscription_ids = array_column($stripe_subscriptions, 'id');
    if (in_array($subid, $subscription_ids)) {
        try {
            // Cancel the subscription immediately
            $subscription = $stripe->subscriptions->cancel($subid, ['at_period_end' => false]);

            // Check the status after cancellation
            if ($subscription->status == 'canceled') {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php', get_string('cancelsuccess', 'enrol_stripepaymentpro'));
            } else {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php', get_string('cancelfailed', 'enrol_stripepaymentpro'));
            }
        } catch (Exception $e) {
            \core\notification::error($e->getMessage());
        }
    } else {
        redirect($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php', get_string('invalidsubid', 'enrol_stripepaymentpro'));
    }
}

// Page header.
echo $OUTPUT->header();
?>

<div class="container">
  <table class="table table-striped">
    <thead>
      <tr>
        <th><?php echo get_string('coursename', 'enrol_stripepaymentpro') ?></th>
        <?php if (is_siteadmin($USER->id)) { ?>
          <th><?php echo get_string('subid', 'enrol_stripepaymentpro') ?></th>
          <th><?php echo get_string('username', 'enrol_stripepaymentpro') ?></th>
        <?php } ?>
        <th><?php echo get_string('status', 'enrol_stripepaymentpro') ?></th>
        <th><?php echo get_string('startdate', 'enrol_stripepaymentpro') ?></th>
        <th><?php echo get_string('nextpayment', 'enrol_stripepaymentpro') ?></th>
        <th><?php echo get_string('action', 'enrol_stripepaymentpro') ?></th>
      </tr>
    </thead>
    <tbody>
      <?php
      foreach ($stripe_subscriptions as $subscription) {
        //get all the data for that subscription id  
      	$local_sub = $subscriptions_by_id[$subscription->id];
        $user = $DB->get_record('user', array('id' => $local_sub->userid));
        $course = $DB->get_record('course', array('id' => $local_sub->courseid));
        $fullname = fullname($user);

        echo '<tr>
        <td>' . $course->fullname . '</td>';
        if (is_siteadmin($USER->id)) {
          echo '<td>' . $subscription->id . '</td>';
          echo '<td>' . $user->username . '</td>';
        }
        echo '<td>' . $subscription->status . '</td>';
        echo '<td>' . date('Y-m-d H:i:s', $subscription->current_period_start) . '</td>';
        echo '<td>' . date('Y-m-d H:i:s', $subscription->current_period_end) . '</td>';
        echo '<td>';
        if ($subscription->status == 'active') {
            echo '<a class="btn btn-secondary" onclick="return confirm(\'' . get_string('areyousure', 'enrol_stripepaymentpro') . '\n' . get_string('areyousure_des', 'enrol_stripepaymentpro') . '\')" href="' . $PAGE->url->out(true, array('subid' => $subscription->id)) . '">' . get_string('cancelsubcription', 'enrol_stripepaymentpro') . '</a>';
        } else {
            echo get_string('notactive', 'enrol_stripepaymentpro');
        }
        echo '</td>';
        echo '</tr>';
      }
      ?>
    </tbody>
  </table>
</div>

<?php
// Page footer.
echo $OUTPUT->footer();
