=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes in /enrol/* - plugins,
information provided here is intended especially for developers.

=== 4.4 ===

* Functions core_enrol_external::search_users and course_enrolment_manager::search_users now have extra optional parameter
  contextid which allows to search users in a specific activity context. When omitted it searches in the whole course.
* New find_instance() function has been created. It finds a matching enrolment instance in a given course using provided
 enrolment data (for example cohort idnumber for cohort enrolment). Defaults to null. Override this function in your
 enrolment plugin if you want it to be supported in CSV course upload. Please be aware that sometimes it is not possible
 to uniquely find an instance based on given data. For example lti entolment records do not have any unique data except
 id in mod_lti_enrol table. Such plugins should not be supported in CSV course upload. The exception is self enrolment
 plugin since it is already supported in CSV course upload. For self enrolment plugin, find_instance() returns first
 available instance in the course.
* A sesskey is no longer passed to the enrol/test_settings.php page so it can no longer be required in test_settings().
* enrol_self_plugin::get_welcome_email_contact() has been deprecated.
  Please use enrol_plugin::get_welcome_message_contact() instead.
* enrol_self_plugin::email_welcome_message() has been deprecated.
  Please use enrol_plugin::send_course_welcome_message_to_user() instead.

=== 4.3 ===

* New is_csv_upload_supported() function has been created. It checks whether enrolment plugin is supported
 in CSV course upload. Defaults to false. Override this function in your enrolment plugin if you want it to
 be supported in CSV course upload.

=== 4.2 ===

* New is_self_enrol_available() function has been created. Similar to can_self_enrol but without checking user capabilities.
* External function core_enrol_external::get_users_courses now returns a field called "courseimage" containing a URL pointing to
  the course image.

=== 4.0 ===

* Final deprecation of the following webservice:
  * core_enrol_edit_user_enrolment
* External function core_enrol_external::get_users_courses now returns the last time a course was modified (timemodified field)
* The behat step i_add_enrolment_method_with() has been deprecated. Please use the new method i_add_enrolment_method_for_with()

=== 3.11 ===

* Added onlysuspended option to core_enrol_get_enrolled_users webservice to retrieve only suspended users.

=== 3.8 ===

* Function enrol_manual_plugin::enrol_cohort now return the number of enrolled cohort users.

=== 3.7 ===

* Functions get_potential_users() and search_other_users() now return more information to avoid extra count query:
  - users: List of user objects returned by the query.
  - moreusers: True if there are still more users, otherwise is False.
  - totalusers: Number users matching the search. (This element only exists if the function is called with $returnexactcount param set to true).
* enrolledusercount is now optional in the return value of get_users_courses() for performance reasons. This is controlled with the new
  optional returnusercount parameter (default true).
* External function core_enrol_external::get_users_courses now returns a new field "completionusertracked" that indicates if the
  given user is being tracked for completion.

=== 3.6 ===

* External function core_enrol_external::get_users_courses now return more information to avoid multiple queries to build the
  user dashboard:
  - displayname: Course display name for lists.
  - marker: Course section active marker.
  - completed: Whether the given user completed the course or not.
  - lastaccess: Last time the user accessed the course.
  - overviewfiles: Course overview files.
  - completionhascriteria: Whether completion criteria is set for the course.
  - isfavourite: Whether the user marked the course as favourite.
  - hidden: Whether the user hide the course from the dashboard.
* External functions core_enrol_external::get_enrolled_users and core_enrol_external::get_enrolled_users_with_capability now return
  the last access time for the users in the given course.

=== 3.5 ===

* Default sorting in enrol_get_my_courses(), enrol_get_all_users_courses() and enrol_get_users_courses() now respects
  the site setting "navsortmycoursessort" and should be consistently used when displaying the courses in the UI.

=== 3.4 ===

* render_course_enrolment_users_table method has been removed from the renderer. The enrolled users page is now
  combined with the participants page. /enrol/users.php no longer exists.

=== 3.3 ===

* External function core_enrol_external::get_users_courses now return the user progress, start and the end course dates.

=== 3.2 ===

* External function core_enrol_external::get_users_courses now return the category id as an additional optional field.

=== 3.1 ===

* core_enrol_external::get_enrolled_users now supports two additional parameters for ordering: sortby and sortdirection.
* Enrolment plugins UI have been consolidated. Plugins can implement use_standard_editing_ui() function
  and add edit_instance_form() and edit_instance_validation() methods instead of providing their own edit.php and form.
  They can then rely on the default implementation of get_action_icons and get_course_navigation. In future this will
  mean they can be called by webservices/user upload tools because they can validate their data.

=== 3.0 ===

* Added new events enrol_instance_created, enrol_instance_updated and
  enrol_instance_deleted . Always trigger them when changing records in the
  DB table 'enrol'.
* Constant CACHE_COURSE_CONTACTS_TTL was deleted.
* External function core_enrol_external::get_enrolled_users now returns only the viewable participants if the course has groups enabled and no groupid is passed.

=== 2.9 ===

* External function core_enrol_external::get_users_courses now returns additional optional fields:
   - summary: Course summary.
   - summaryformat: Course summary format.
   - format: Course format.
   - showgrades: True if grades are shown, otherwise false.
   - lang: Forced course language.
   - enablecompletion: Control or not via completion and activity settings.

=== 2.8 ===

* enrol_plugin::instance_deleteable() is deprecated and has been replaced by enrol_plugin::can_delete_instance()
* enrol_plugin::can_hide_show_instance() is a new function to control who can hide/show enrolment instances.
  Returns true by default but plugins must implement their own logic.

=== 2.6 ===

* Enrolment plugin which supports self enrolment should implement can_self_enrol()
* Enrolment plugin should implement get_enrol_info() to expose instance information
  with webservice or external interface.
* Webservice core_enrol_get_enrolled_users_with_capability was incorrectly specifing
  float as the return type for user id. int is the actual returned type and is now
  reported as such.

=== 2.5 ===

* plugins may use general enrol/editenrolment.php page to let users edit
  enrolments manually
* new support for grade recovery in enrol_plugin::enrol_user() method

=== 2.4 ===

required changes in code:
* use role_get_name() or role_fix_names() if you need any role names, using role.name
  directly from database is not correct any more
* new restore support: ENROL_RESTORE_EXACT, ENROL_RESTORE_NOUSERS
  and ENROL_RESTORE_CLASS were removed, implement new restore_* plugin methods instead

other changes and new features:
* course enrolment manager now works with disabled plugins too
* new support for protected group membership linked to enrol instance
* new support for expiry notifications - see enrol_plugin::send_expiry_notifications() method


=== 2.2 ===

required changes in code:
* load_temp_role() is deprecated, use load_temp_course_role() instead, temp role not loaded
* remove_temp_role() is deprecated, use remove_temp_course_roles() instead
* 'user_unenrol_modified' event was renamed to 'user_enrol_modified'


=== 2.0 ===

required changes in code:
* enrolment plugins need to be rewritten to use new API - see inline phpdocs and official plugins
