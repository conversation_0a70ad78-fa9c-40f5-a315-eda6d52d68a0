{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_lti/local/ltiadvantage/platform_details

    Template which displays a table containing platform configuration details.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * platform_details_info
    * platform_details
    * edit_platform_details_url

    Example context (json):
    {
        "platform_details_info": "help text explaining where to get these details from, etc",
        "platform_details": [
            {
                "name": "name",
                "value": "Moodle LMS"
            },
            {
                "name": "Platform ID",
                "value": "https://lms.example.com/"
            },
            {
                "name": "Client ID",
                "value": "ab46f8ea123"
            }
        ],
        "edit_platform_details_url": "https://SITE/enrol/lti/register_platform.php?action=edit&id=x"
    }
}}
<div class="alert alert-info alert-block">
    {{{platform_details_info}}}
</div>
<table class="admintable generaltable">
    <tbody>
    {{#platform_details}}
        <tr>
            <th class="col-3">{{name}}</th>
            <td class="col-9">
                {{#value}}{{.}}{{/value}}
                {{^value}}-{{/value}}
            </td>
        </tr>
    {{/platform_details}}
    </tbody>
</table>
<a class="btn btn-secondary" href="{{edit_platform_details_url}}">{{#str}}editplatformdetails, enrol_lti{{/str}}</a>
