{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_lti/local/ltiadvantage/cookies_required_notice

    Displays a notice, reporting that cookies are required but couldn't be set.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * heading
    * notification

    Example context (json):
    {
        "heading": "Cookies are required",
        "notification": {
            "message": "You appear to be using an unsupported browser...",
            "extraclasses": "",
            "announce": true,
            "closebutton": false,
            "issuccess": false,
            "isinfo": false,
            "iswarning": true,
            "iserror": false
        }
    }
}}
<h3>{{heading}}</h3>
{{#notification}}
    {{> core/notification}}
{{/notification}}
