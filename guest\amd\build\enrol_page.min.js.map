{"version": 3, "file": "enrol_page.min.js", "sources": ["../src/enrol_page.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Functions for the enrol_guest plugin\n *\n * @module     enrol_guest/enrol_page\n * @copyright  Marina Glancy\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport ModalForm from 'core_form/modalform';\nimport {getString} from 'core/str';\nimport {prefetchStrings} from 'core/prefetch';\nimport Url from 'core/url';\n\n/**\n * Initialise widget on the course enrolment page - clicking on the button should submit the form\n *\n * @param {Number} instanceId\n */\nexport function initEnrol(instanceId) {\n    prefetchStrings('moodle', [\n        'loginguest',\n    ]);\n\n    const button = document.querySelector('button[type=\"submit\"][data-instance=\"' + instanceId + '\"]');\n    if (button) {\n        button.addEventListener('click', (e) => {\n            e.preventDefault();\n            const modalForm = new ModalForm({\n                modalConfig: {\n                    title: button.dataset.title,\n                    large: false, // This is a very small form that does not need a large popup.\n                },\n                formClass: button.dataset.form,\n                args: {id: button.dataset.id, instance: instanceId},\n                saveButtonText: getString('loginguest', 'moodle'),\n                returnFocus: button,\n            });\n\n            // Redirect to the course page when the form is submitted.\n            modalForm.addEventListener(modalForm.events.FORM_SUBMITTED, event => {\n                window.location.href = event.detail ? event.detail :\n                    Url.relativeUrl('/course/view.php', {id: button.dataset.id});\n            });\n\n            modalForm.show();\n        });\n    }\n}\n"], "names": ["instanceId", "button", "document", "querySelector", "addEventListener", "e", "preventDefault", "modalForm", "ModalForm", "modalConfig", "title", "dataset", "large", "formClass", "form", "args", "id", "instance", "saveButtonText", "returnFocus", "events", "FORM_SUBMITTED", "event", "window", "location", "href", "detail", "Url", "relativeUrl", "show"], "mappings": ";;;;;;;yFAiC0BA,0CACN,SAAU,CACtB,qBAGEC,OAASC,SAASC,cAAc,wCAA0CH,WAAa,MACzFC,QACAA,OAAOG,iBAAiB,SAAUC,IAC9BA,EAAEC,uBACIC,UAAY,IAAIC,mBAAU,CAC5BC,YAAa,CACTC,MAAOT,OAAOU,QAAQD,MACtBE,OAAO,GAEXC,UAAWZ,OAAOU,QAAQG,KAC1BC,KAAM,CAACC,GAAIf,OAAOU,QAAQK,GAAIC,SAAUjB,YACxCkB,gBAAgB,kBAAU,aAAc,UACxCC,YAAalB,SAIjBM,UAAUH,iBAAiBG,UAAUa,OAAOC,gBAAgBC,QACxDC,OAAOC,SAASC,KAAOH,MAAMI,OAASJ,MAAMI,OACxCC,aAAIC,YAAY,mBAAoB,CAACZ,GAAIf,OAAOU,QAAQK,QAGhET,UAAUsB"}