<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * web service defination
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

 
defined('MOODLE_INTERNAL') || die();

$services = array(
    'moodle_enrol_stripepaymentpro' => array(                      //the name of the web service
        'functions' => array('moodle_stripepaymentpro_couponsettings', 'moodle_stripepaymentpro_stripe_js_settings','moodle_stripepaymentpro_deactivate_coupon', 'moodle_stripepaymentpro_deactivate_all_coupons','moodle_stripepaymentpro_webhook_handler'), //web service functions of this service
        'requiredcapability' => '',                //if set, the web service user need this capability to access 
        //any function of this service. For example: 'some/capability:specified'                 
        'restrictedusers' => 0,                      //if enabled, the Moodle administrator must link some user to this service
        //into the administration
        'enabled' => 1,                               //if enabled, the service can be reachable on a default installation
        'shortname' => 'enrolstripepaymentpro' //the short name used to refer to this service from elsewhere including when fetching a token
    )
);
$functions = array(
    'moodle_stripepaymentpro_couponsettings' => [
        'classname' => 'moodle_enrol_stripepaymentpro_external',
        'methodname' => 'stripepaymentpro_couponsettings',
        'classpath' => 'enrol/stripepaymentpro/externallib.php',
        'description' => 'Load coupon settings data',
        'type' => 'write',
        'ajax' => true,
        'loginrequired' => true,
    ],
    'moodle_stripepaymentpro_stripe_js_settings' => array(
        'classname' => 'moodle_enrol_stripepaymentpro_external',
        'methodname' => 'stripe_js_method',
        'classpath' => 'enrol/stripepaymentpro/externallib.php',
        'description' => 'Update information after Stripe Successful Connect',
        'type' => 'write',
        'ajax' => true,
        'loginrequired' => true,
    ),
    'moodle_stripepaymentpro_deactivate_coupon' => array(
        'classname'   => 'moodle_enrol_stripepaymentpro_external',
        'methodname'  => 'deactivate_coupon',
        'classpath'   => 'enrol/stripepaymentpro/externallib.php',
        'description' => 'Deactivate a coupon for a course',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities'=> 'moodle/site:config',
    ),
    'moodle_stripepaymentpro_deactivate_all_coupons' => array(
        'classname'   => 'moodle_enrol_stripepaymentpro_external',
        'methodname'  => 'deactivate_all_coupons',
        'classpath'   => 'enrol/stripepaymentpro/externallib.php',
        'description' => 'Deactivate all coupons for a course',
        'type'        => 'write',
        'ajax'        => true,
        
    ),
	'moodle_stripepaymentpro_webhook_handler' => array(
        'classname'   => 'moodle_enrol_stripepaymentpro_external',
        'methodname'  => 'webhook_handler',
        'classpath'   => 'enrol/stripepaymentpro/externallib.php',
        'description' => 'webhook handler',
        'type'        => 'write',
        'ajax'        => true,
        
    ),
);