{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_lti/copy_grid

    The content to display when editing a tool.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * rows An array of objects with label, hidelabel, text and id

    Example context (json):
    {
        "rows": [
            {
                "label": "Tool URL",
                "text": "http://example.com/",
                "id": "toolurl",
                "hidelabel": false
            },
            {
                "label": "Secret",
                "text": "ABCDEF1234567890",
                "id": "secret",
                "hidelabel": true
            }
        ]
    }
}}
{{#rows}}
    <div>
        <label style="display: inline-block; width: 5em"
            {{#id}}for="{{id}}-{{uniqid}}"{{/id}} {{#hidelabel}}
               class="accesshide"{{/hidelabel}}>{{label}}</label>
        <div style="display: inline-block">{{> core/copy_box }}</div>
    </div>
{{/rows}}
