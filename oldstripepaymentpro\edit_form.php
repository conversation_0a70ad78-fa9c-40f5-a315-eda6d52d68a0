<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
/**
 * Course wise edit form.
 *
 * Adds new instance of enrol_stripepaymentpro to specified course
 * or edits current instance.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();
global $CFG;
require_once($CFG->libdir.'/formslib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/lib.php');
require_once('lib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');
use \Stripe\Stripe as Stripe;
use \Stripe\Price as Price;
class enrol_stripepaymentpro_edit_form extends moodleform {
    /**
     * Sets up moodle form.
     * @return void
     */
    public function definition() {
        global $CFG;
        $plugin = enrol_get_plugin('stripepaymentpro');
        $mform = $this->_form;
        list($instance, $plugin, $plugincore, $context) = $this->_customdata;
        $options = array(ENROL_INSTANCE_ENABLED  => get_string('yes'),
                         ENROL_INSTANCE_DISABLED => get_string('no'));
        $mform->addElement('select', 'status', get_string('status', 'enrol_stripepaymentpro'), $options);
        $mform->setDefault('status', get_config('enrol_stripepaymentpro', 'status'));
        // cost
        $costarray = array();
        $costarray[] = $mform->createElement('text', 'cost', get_string('cost', 'enrol_stripepaymentpro'), array('size' => 4));
        // $mform->setDefault('cost', format_float($plugin->get_config('cost'), 2, true));
        $mform->setType('cost', PARAM_INT);
        $mform->addGroup($costarray, 'costar', get_string('cost', 'enrol_stripepaymentpro'), array(' '), false);
        
        $mform->addElement('checkbox', 'recurringproduct', get_string('recurringcost', 'enrol_stripepaymentpro'));
        $mform->setDefault('recurringproduct', 1);

        // Recurring Cost
        $mform->addElement('text', 'recurringcost', get_string('recurringcost', 'enrol_stripepaymentpro'));
        $mform->setType('recurringcost', PARAM_FLOAT);
        $mform->setDefault(
            'recurringcost',
            isset($instance->customtext4) && $instance->customtext4 > 0
                ? Price::retrieve($instance->customtext4)->unit_amount / $plugin->get_fractional_unit_amount($instance->currency)
                : ''
        );
        $mform->disabledIf('recurringcost', 'recurringproduct', 'notchecked');
        
        //productid
        $mform->addElement('static', 'customtext2', get_string('productid', 'enrol_stripepaymentpro'));
        $mform->setDefault('customtext2', 'This will fill automaticaly');
        $mform->setType('customtext2', PARAM_TEXT);
        $renewalintarval = $plugin->get_renewalintarval();
        $mform->addElement('select', 'customint4', get_string('renewalintarval', 'enrol_stripepaymentpro'), $renewalintarval);
        $mform->disabledIf('customint4', 'recurringproduct', 'notchecked');
        $mform->addElement('text', 'customint1', get_string('renewalintarvalnum', 'enrol_stripepaymentpro'));
        $mform->setDefault('customint1', '7');
        $mform->setType('customint1', PARAM_RAW);
        $mform->addHelpButton('customint1', 'renewalintarvalnum', 'enrol_stripepaymentpro');
        $mform->disabledIf('customint1', 'recurringproduct', 'notchecked');
        // Currency select
        $currency = enrol_get_plugin('stripepayment')->get_currencies();
        $mform->addElement('select', 'currency', get_string('currency', 'enrol_stripepaymentpro'), $currency);
        $mform->setDefault('currency', get_config('enrol_stripepayment', 'currency'));
        // Trial Period
        $units = array(
            DAYSECS
        );
        $options = array ('units' => $units);
        $mform->addElement('duration', 'customint2', get_string('trialperiod', 'enrol_stripepaymentpro'),
        array('optional' => true, 'defaultunit' => 86400, 'units' => $units));
        $mform->setDefault('trialperiod', 'customint2');
        // Assign role
        if ($instance->id) {
            $roles = get_default_enrol_roles($context, $instance->roleid);
        } else {
            $roles = get_default_enrol_roles($context, get_config('enrol_stripepayment', 'roleid'));
        }
        $mform->addElement('select', 'roleid', get_string('assignrole', 'enrol_stripepaymentpro'), $roles);
        $mform->setDefault('roleid', get_config('enrol_stripepayment', 'roleid'));
        //Max enrolled users
        $mform->addElement('text', 'customint3', get_string('maxenrolled', 'enrol_stripepaymentpro'));
        $mform->setDefault('maxenrolled', 'customint3');
        $mform->addHelpButton('customint3', 'maxenrolled', 'enrol_stripepaymentpro');
        $mform->setType('customint3', PARAM_INT);
        // Enrolment duration
        $mform->addElement('duration', 'enrolperiod', get_string('enrolperiod', 'enrol_stripepaymentpro'),
        array('optional' => true, 'defaultunit' => 86400));
        $mform->setDefault('enrolperiod', get_config('enrol_stripepayment', 'enrolperiod'));
        $mform->addHelpButton('enrolperiod', 'enrolperiod', 'enrol_stripepaymentpro');
        // Start date
        $mform->addElement('date_time_selector', 'enrolstartdate', get_string('enrolstartdate', 'enrol_stripepaymentpro'),
        array('optional' => true));
        $mform->setDefault('enrolstartdate', 0);
        $mform->addHelpButton('enrolstartdate', 'enrolstartdate', 'enrol_stripepaymentpro');
        // End date
        $mform->addElement('date_time_selector', 'enrolenddate', get_string('enrolenddate', 'enrol_stripepaymentpro'),
        array('optional' => true));
        $mform->setDefault('enrolenddate', 0);
        $mform->addHelpButton('enrolenddate', 'enrolenddate', 'enrol_stripepaymentpro');
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);
        $mform->addElement('hidden', 'courseid');
        $mform->setType('courseid', PARAM_INT);
        if (enrol_accessing_via_instance($instance)) {
            $mform->addElement('static', 'selfwarn', get_string('instanceeditselfwarning', 'core_enrol'),
            get_string('instanceeditselfwarningtext', 'core_enrol'));
        }
        $this->add_action_buttons(true, ($instance->id ? null : get_string('addinstance', 'enrol')));
        $this->set_data($instance);
    }
    /**
     * Sets up moodle form validation.
     * @param stdClass $data
     * @param stdClass $files
     * @return $error error list
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);
        if (!empty($data['enrolenddate']) and $data['enrolenddate'] < $data['enrolstartdate']) {
            $errors['enrolenddate'] = get_string('enrolenddaterror', 'enrol_stripepaymentpro');
        }
        $cost = str_replace(get_string('decsep', 'langconfig'), '.', $data['cost']);
        if (!is_numeric($cost)) {
            $errors['cost'] = get_string('costerror', 'enrol_stripepaymentpro');
        }
        return $errors;
    }
}
