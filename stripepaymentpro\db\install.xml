<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="enrol/stripepaymentpro/db" VERSION="20120122" COMMENT="XMLDB file for Moodle enrol/stripepaymentpro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
  <TABLES>
    <TABLE NAME="enrol_stripepaymentpro" COMMENT="Holds all known information about Stripe transactions">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" />
        <FIELD NAME="business" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="receiver_email" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="receiver_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="item_name" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="coupon_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="memo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="tax" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="option_name1" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="option_selection1_x" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="option_name2" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="option_selection2_x" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="payment_status" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="pending_reason" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="txn_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="parent_txn_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="payment_type" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="product_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="product_name" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="product_type" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="subscription_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="renewal_interval" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="renewal_intervalperiod" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="trialperiodend" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="timeupdated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id" />
      </KEYS>
    </TABLE>

    <TABLE NAME="enrol_stripepro_coupons" COMMENT="Associates coupons with courses">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" />
        <FIELD NAME="couponid" TYPE="char" LENGTH="255" NOTNULL="true" />
        <FIELD NAME="coupon_name" TYPE="char" LENGTH="255" NOTNULL="true" />
        <FIELD NAME="amount_off" TYPE="float" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="2" />
        <FIELD NAME="percent_off" TYPE="float" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="2" />
        <FIELD NAME="currency" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="duration" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="no_of_months" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="stripe_product_id" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="coupon_expiry" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id" />
        <KEY NAME="unique_couponid" TYPE="unique" FIELDS="couponid" />
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>