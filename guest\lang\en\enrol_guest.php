<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'enrol_guest', language 'en'.
 *
 * @package    enrol_guest
 * @copyright  2010 onwards Petr Skoda  {@link http://skodak.org}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['allowguests'] = 'This course allows guest users to enter';
$string['guest:config'] = 'Configure guest access instances';
$string['guestaccess_withpassword'] = 'Guest access requires password';
$string['guestaccess_withoutpassword'] = 'Guest access';
$string['password'] = 'Password';
$string['password_help'] = 'A password allows guest access to the course to be restricted to only those who know the password. Guests will be required to supply the password each time they access the course.';
$string['passwordinvalid'] = 'Incorrect access password, please try again';
$string['passwordinvalidhint'] = 'That access password was incorrect, please try again<br />
(Here\'s a hint - it starts with \'{$a}\')';
$string['passwordrequired'] = 'A password will be required.';
$string['pluginname'] = 'Guest access';
$string['pluginname_desc'] = 'Guest access plugin is only granting temporary access to courses, it is not actually enrolling users.';
$string['requirepassword'] = 'Require guest access password';
$string['requirepassword_desc'] = 'Require access password in new courses and prevent removing of access password from existing courses.';
$string['showhint'] = 'Show hint';
$string['showhint_desc'] = 'Show first letter of the guest access password.';
$string['status'] = 'Allow guest access';
$string['status_desc'] = 'Allow temporary guest access by default.';
$string['status_help'] = 'This setting determines whether a user can access the course as a guest, without being required to enrol.';
$string['status_link'] = 'enrol/guest';
$string['usepasswordpolicy'] = 'Use password policy';
$string['usepasswordpolicy_desc'] = 'Use standard password policy for guest access passwords.';
$string['privacy:metadata'] = 'The Guest access enrolment plugin does not store any personal data.';
