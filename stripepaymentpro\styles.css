.strip-subcription-plan-details{
	max-width: 400px;
	width: 100%;
	margin: auto;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	gap: 0.25rem;
	margin-bottom: 0.75rem;
}
.strip-subcription-plan-details.details-content{
	flex-direction: column;
}
.strip-subcription-plan-details p{
	margin: 0;
	font-weight: 600;
}

button#apply {
        color: #fff;
        border: 0;
        padding: 5px 16px;
        border-radius: 0.5rem;
        font-size: 13px;
    }
    button#payButton,
    button#card-button-zero {
        color: #fff;
        border: 0;
        padding: 5px 32px;
        border-radius: 0.25rem;
        font-size: 13px;
        box-shadow: 0 0.125rem 0.25rem #645cff2e;
        width: 100%;
    }
.displaynone{
        display: none;
    }

.container:has(.table-striped){
	margin: 2rem 0 0 0;
	max-width: 100%;
    padding: 0;
    overflow-x: auto;
}

.table-striped thead th{
	vertical-align: middle;
}

.table-striped .btn-secondary:not(:disabled):not(.disabled):active:focus{
	box-shadow: none;
	outline: none;
}
.table-striped .btn-secondary:focus, .btn-secondary.focus{
	outline: none;
	box-shadow: none;
}

.generate-coupon-section{
	display: none;
}

#page.drawers div[role="main"]:has(.strip-coupon-navigation-section){
	padding: 0 !important; //
}



.strip-coupon-navigation-section {
  display: flex;
  max-width: -moz-fit-content;
  max-width: fit-content;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  margin-bottom: 1rem;
}
.strip-coupon-navigation-section button {
  background-color: transparent;
  padding: 0.5rem;
  cursor: pointer;
  border: none;
  outline: none;
  box-shadow: none;
}
.strip-coupon-navigation-section button.active {
  color: #0f6cbf;
  border-bottom: 1px solid #0f6cbf;
}

.all-coupons-section {
  overflow: auto;
  width: 100%;
}
.all-coupons-section table {
  width: 100%;
}
.all-coupons-section table thead tr {
  padding: 0.25rem;
}
.all-coupons-section table thead tr th {
  padding: 0.5rem 1rem;
  border: 1px dotted #dee2e6;
}
.all-coupons-section table tbody tr {
  padding: 0.25rem;
}
.all-coupons-section table tbody tr td {
  padding: 0.5rem 1rem;
  border: 1px dotted #dee2e6;
  min-height: 53px;
}
.all-coupons-section table tbody tr td.coupon-list {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  flex-wrap: wrap;
}
.all-coupons-section table tbody tr td .coupon-name {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: #ececec;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}
.all-coupons-section table tbody tr td .coupon-name button {
  background: transparent;
  padding: 0;
  outline: none;
  border: none;
  box-shadow: none;
  font-size: 0.75rem;
  color: rgba(188, 0, 0, 0.8509803922);
}
.all-coupons-section table tbody tr td .deactivate-all-coupons {
  outline: none;
  border: none;
  box-shadow: none;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  background-color: #ececec;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  border-radius: 0.25rem;
}
.all-coupons-section table tbody tr td .deactivate-all-coupons i {
  background: transparent;
  padding: 0;
  outline: none;
  border: none;
  box-shadow: none;
  font-size: 0.75rem;
  color: rgba(188, 0, 0, 0.8509803922);
}