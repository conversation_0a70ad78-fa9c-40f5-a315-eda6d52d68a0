<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once("$CFG->libdir/externallib.php");
require_once("$CFG->libdir/enrollib.php");
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');
require_once($CFG->dirroot.'/enrol/stripepaymentpro/classes/helper/webhook_helper.php');
use enrol_stripepaymentpro\helper\webhook_helper;
use \Stripe\Stripe as Stripe;
use \Stripe\Checkout\Session as Session;
use \Stripe\Coupon as Coupon;
use \Stripe\Price as Price;


/**
 * Stripe enrolment plugin.
 *
 * External library for webservices calls
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class moodle_enrol_stripepaymentpro_external extends external_api {

	/**
	 * parameter type for stripepaymentpro_couponsettings
	 */ 
    public static function stripepaymentpro_couponsettings_parameters() {
        return new external_function_parameters(
            [
                'coupon_name' => new external_value(PARAM_RAW, 'The coupon id to operate on'),
                'instanceid' => new external_value(PARAM_INT, 'Update instance id'),
            ]
        );
    }

    /**
     * return type of couponsettings functioin
     */
    public static function stripepaymentpro_couponsettings_returns() {
        return new external_single_structure(
            [
                'coupon_information' => new external_value(PARAM_RAW, 'coupon_information: true if success')
            ]
        );
    }

    /**
     * function for couponsettings and give discount .
     * @param number $couponid
     * @param number $instanceid
     * @return array
     */
    public static function stripepaymentpro_couponsettings($coupon_name, $instanceid) {
        global $DB, $CFG;
        $plugin = enrol_get_plugin('stripepayment');
        
        $plugininstance = $DB->get_record("enrol", ["id" => $instanceid, "status" => 0]);
        if (!$plugininstance) {
            throw new invalid_parameter_exception('Invalid instance ID');
        }

        $cost = (float)$plugininstance->cost > 0 ? (float)$plugininstance->cost : (float)get_config('enrol_stripepayment', 'cost');
        $cost = format_float($cost, 2, false);

        try {
            $get_coupon_details = $DB->get_record('enrol_stripepro_coupons', ['coupon_name' => $coupon_name]);
            
            if ( $get_coupon_details != null ) {
                return ['coupon_information' => json_encode((array) $get_coupon_details)];
            } else {
                \core\notification::error(get_string('invalidcoupon', 'enrol_stripepayment'));
            }
        } catch (Exception $e) {
            throw new invalid_parameter_exception($e->getMessage());
        }

        return ['coupon_information' => ""];
    }

	/** 
	 * parameter declairation of stripe_js_method
	 */
    public static function stripe_js_method_parameters() {
        return new external_function_parameters(
            array(
                'user_id' => new external_value(PARAM_RAW, 'Update data user id'),
                'couponid' => new external_value(PARAM_RAW, 'Update coupon id'),
                'instance_id' => new external_value(PARAM_RAW, 'Update instance id'),
            )
        );
    }
	
	/**
	 * declairation of return type of stripe_js_method
	 */
    public static function stripe_js_method_returns() {
        return new external_single_structure(
            array(
                'paymentintent' => new external_value(PARAM_RAW, 'The payment intent data in raw format', VALUE_OPTIONAL),
                'session' => new external_value(PARAM_RAW, 'The checkout session data in raw format', VALUE_OPTIONAL)
            )
        );
    }

	/**
	 * function for creating checkout session for users fpr payment
	 * @param $user_id number user id of the student
	 * @param $couponid value of the coupon id if applied coupon
	 * @param instance id of the plugininstance 
	 */
    public static function stripe_js_method($user_id, $couponid, $instance_id) {
        global $CFG, $DB, $PAGE;
        $plugin = enrol_get_plugin('stripepaymentpro');
        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        $payment_gateway_type = get_config('enrol_stripepaymentpro', 'payment_gateway_type');
        $enrol = null;
        if (!$enrol = $DB->get_record("enrol", array("id" => $instance_id, "status" => 0))) {
            self::message_stripepaymentpro_error_to_admin("Not a valid instance id", [ "id" => $instance_id ]);
            redirect($CFG->wwwroot . '/course/view.php?id=' . $enrol->courseid);
        }
        $courseid = $enrol->courseid;

        // Validate course record
        if (!$course = $DB->get_record("course", array("id" => $courseid))) {
            self::message_stripepaymentpro_error_to_admin(get_string('invalidcourseid', 'enrol_stripepaymentpro'),["id" => $courseid]);
            redirect($CFG->wwwroot);
        }
        $currency = $enrol->currency;
        // Validate course context
        if (! $context = context_course::instance($course->id, IGNORE_MISSING)) {
            self::message_stripepaymentpro_error_to_admin(get_string('invalidcontextid', 'enrol_stripepaymentpro'),["id" => $course->id]);
            redirect($CFG->wwwroot);
        }
    
        // Validate user record
        if (!$user = $DB->get_record("user", array("id" => $user_id))) {
            self::message_stripepaymentpro_error_to_admin("Not a valid user id", ["id" => $user_id]);
            redirect($CFG->wwwroot . '/course/view.php?id=' . $courseid);
        }
        $PAGE->set_context($context);
        // Validate secret key and course ID
        if (empty($secretkey) || empty($courseid)) {
            redirect($CFG->wwwroot . '/course/view.php?id=' . $courseid);
        } else {
          
        	//get recurring priceid in 
            $recurringpriceid = $enrol->customtext4;
            Stripe::setApiKey($secretkey);
            
            // Retrieve or create Stripe customer
            $checkcustomer = $DB->get_records('enrol_stripepaymentpro', array('receiver_email' => $user->email));
            $receiver_id = null;
    
            foreach ($checkcustomer as $keydata => $valuedata) {
                $checkcustomer = $valuedata;
            }
        
        	// for giving user details in checkout customer id 
        	// or customer email only one should be there 
            if ($checkcustomer) {
                $receiver_id = $checkcustomer->receiver_id;
                $receiver_email = null;   // must not be set if customer id provided
            } else {
                $receiver_id = null;  // Stripe will create customer id in checkout
                $receiver_email = $user->email;
            }
            // Retrieve price details
            $price = Price::retrieve($enrol->customtext3); // The sign up fee is stored in customtext3 feild in enrol
			// New price for discount object 
        	$amount = $price->unit_amount;
        	$discountedpriceobject = $price;

            if ($couponid) {
                $coupon = \Stripe\Coupon::retrieve($couponid);
                if( $coupon->duration === 'once' ) {
                    $discountedpriceobject = $plugin->create_discounted_price_object($discountedpriceobject, $amount, $couponid, $currency, $enrol);
                }
            }

            $enable_automatic_tax = !empty(get_config('enrol_stripepaymentpro', 'enable_automatic_tax')) ? true : false;
            try {
                $session_params = [
                    'customer' => $receiver_id,
                    'customer_email' => $receiver_email,
                    'mode' => 'payment',
                    'line_items' => [
                        [
                            'price' => $discountedpriceobject->id,
                            'quantity' => 1,
                        ],
                    ],
                    'metadata' => [
                        'userid' => $user_id,
                        'couponid' => $couponid,
                        'instanceid' => $instance_id,
                    ]
                ];

                if($recurringpriceid > 0) {
                    $session_params['mode'] = 'subscription';
                    $session_params['line_items'][] =  [
                            'price' =>$recurringpriceid,
                            'quantity' => 1,
                    ];
                }

                if ($enable_automatic_tax) {
                    $session_params['automatic_tax'] = ['enabled' => true]; // Enable automatic tax
                    $session_params['billing_address_collection'] = 'required'; // Force user to enter billing address
                    $session_params['tax_id_collection'] = ['enabled' => true];
                }

                // only pass the coustomer value if it is not null
                if ($receiver_id) {
                    $session_params['customer_update'] = ['address' => 'auto']; // Let Stripe auto-update customer address
                }

                if ($enrol->customint2 > 0) {
                    $session_params['subscription_data']['trial_end'] = time() + $enrol->customint2;
                }

                // we haveto set disount feild on session param if the coupon duration is repeating 
                if($couponid && ($coupon->duration === 'repeating'|| $coupon->duration === 'forever')) {
                    $session_params['discounts'] = [['coupon' => $couponid]];
                }

            	//the config is set in settings panel will be work here 
                if ($payment_gateway_type == 'elements') {

                    $session_params['ui_mode'] = 'embedded';
                    $session_params['return_url'] = $CFG->wwwroot . '/enrol/stripepaymentpro/thankyou.php?session_id={CHECKOUT_SESSION_ID}';

                    $checkout = Session::create($session_params);

                    return ['paymentintent' => json_encode($checkout)];

                } else if ($payment_gateway_type == 'checkout') {
                    $session_params['success_url'] = $CFG->wwwroot . '/enrol/stripepaymentpro/thankyou.php?session_id={CHECKOUT_SESSION_ID}';
                    try {
                        $session = Session::create($session_params);
                    } catch ( Exception $e) {
                        \core\notification::error($e->getMessage());
                    }
                    return [ 'session' => json_encode($session)];
                }
            } catch (Exception $e) {
                throw new moodle_exception($e->getMessage());
            }
        }
    }

	/**
	 * parametere defination deactivate_coupon
	 */
    public static function deactivate_coupon_parameters() {
        return new external_function_parameters(
            array(
                'stripe_product_id' => new external_value(PARAM_RAW, 'Stripe Product ID'),
                'couponid' => new external_value(PARAM_RAW, 'Coupon ID')
            )
        );
    }
	
	/**
	 * function for deactivate a single coupon from a single course
	 * @param $stripe_product_id number target stripe product id for which the coupon will be deactivate
	 * @param $couponid number coupon id which will be deactivate for specific coupon. 
	 */
    public static function deactivate_coupon($stripe_product_id, $couponid){
        global $DB;
        try {  
            $DB->delete_records('enrol_stripepro_coupons',['stripe_product_id' => $stripe_product_id, 'couponid' => $couponid]);
            return true;
        } catch (Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }
        
    }

	/**
	 * return type for deactivate_coupon
	 */
    public static function deactivate_coupon_returns() {
        return new external_value(PARAM_BOOL, 'True if coupon deleted');
    }


    /**
	 * parametere defination deactivate_all_coupons
	 */
    public static function deactivate_all_coupons_parameters() {
        return new external_function_parameters(
            array(
                'stripe_product_id' => new external_value(PARAM_INT, 'Stripe Product ID')
            )
        );
    }

	/**
	 * deactivate all coupon for that specific course.
	 * @param $courseid number for which course the all coupon should be de activated
	 */ 
    public static function deactivate_all_coupons( $stripe_product_id ) {
        global $DB;
        try {
            $coupons = $DB->get_records('enrol_stripepro_coupons', array('stripe_product_id' => $stripe_product_id));
            foreach ($coupons as $coupon) {
                // Delete the record from the local database
                $DB->delete_records('enrol_stripepro_coupons', array('stripe_product_id' => $stripe_product_id, 'couponid' => $coupon->couponid));
            }
            return true;
        } catch(Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }
    }

	/**
	 * return type of deactivate_all_coupons
	 */
    public static function deactivate_all_coupons_returns() {
        return new external_value(PARAM_BOOL, 'True if coupon deleted');
    }


	/**
	 * function for send any error to admn if any error occurs
	 * @param $subject subject of the error message 
	 */
    public static function message_stripepaymentpro_error_to_admin($subject, $data) {
        $admin = get_admin();
        $site = get_site();
    	 $dataString = '';
    	if (is_array($data)) {
        foreach ($data as $key => $value) {
            $dataString .= $key . ": " . $value . "\n";
        }
    }
        $message = "$site->fullname: Transaction failed.\n\n$subject\n\n$dataString\n\n";
        $subject = "STRIPE PAYMENT ERROR: ".$subject;
        $fullmessage = $message;
        $fullmessagehtml = html_to_text('<p>'.$message.'</p>');
        // Send test email.
        ob_start();
        email_to_user($admin, $admin, $subject, $fullmessage, $fullmessagehtml);
        ob_get_contents();
        ob_end_clean();
    }


	/**
	 * parameter defination for  webhook_handler
	 * its empty because event can be many types so the parameter can be different type 
	 */ 
	public static function webhook_handler_parameters() {
		return new external_function_parameters([
        	
        ]);
	}

	/**
	 * function of listinig to the events comes from stripe
	 */
	public static function webhook_handler() {
    	$payload = @file_get_contents('php://input');
		$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
		
    	//getting the webhook secret from config which was set in the time of activating license
    	$endpoint_secret = get_config('enrol_stripepaymentpro', 'stripe_webhook_secret');
    	
    	try {
        	$event = \Stripe\Webhook::constructEvent(
    			$payload, $sig_header, $endpoint_secret
  			);
        }catch(Exception $e) {
        	exit();
        }
    	$web_hook_helper = new webhook_helper($event->data->object->id);

        switch($event->type){
                 	//if the check out has sucessfully paid then enrol the user to the course
        	case 'checkout.session.completed':
        		$web_hook_helper->successfull_enrolment();
        		break;
        	// if the subscription is deleted then check the  end date and if necessery unenrol the user
            case 'customer.subscription.deleted':
				$web_hook_helper->unenrol_user_on_subscription_deleted($event->data->object->id);
        		break;
    	}
    	
		return true;
	}

	/**
	 * return type for webhook_handler
	 */
	public static function webhook_handler_returns() {
		return new external_value(PARAM_RAW, 'status', 999 );
	}
}
