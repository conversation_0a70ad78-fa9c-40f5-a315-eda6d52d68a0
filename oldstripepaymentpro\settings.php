<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin settings.
 *
 * @package    enrol_stripepaymentpro
 */

use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_license_controller;
use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_webhook_controller;

defined('MOODLE_INTERNAL') || die();

$rest_web_link = $CFG->wwwroot . '/admin/settings.php?section=webserviceprotocols';
$create_token = $CFG->wwwroot . '/admin/webservice/tokens.php';
$order_id = get_config('enrol_stripepaymentpro', 'subscriptionid');

// Handle the settings save action.
$error = null;
$success_message = null;

// Initialize the license controller and handle license status.
if (optional_param('stripepaymentpro_license_set_status', '', PARAM_RAW)) {
    $license_controller = new enrol_stripepaymentpro_license_controller();
    $licensedata = $license_controller->add_data();
    if(isset($licensedata) && $licensedata != null) {
        if (isset($licensedata->error)) {
            $error = $licensedata->error;
        } else {
            // Construct the success message based on the current action (activation/deactivation)
            $activations_remaining = $licensedata->data->activations_remaining;
            $total_activations_purchased = $licensedata->data->total_activations_purchased;

            if (isset($licensedata->deactivated)) {
                $success_message = "License deactivated successfully. {$activations_remaining} out of {$total_activations_purchased} activations remaining. Your webhook endpoint is deleted";
            } else if (isset($licensedata->activated)) {
                $success_message = "License activated successfully. {$activations_remaining} out of {$total_activations_purchased} activations remaining. Your webhook endpoint is ready ";
            }
        }
    }
}

// Set the activation setting to inactive by default
if (strpos(get_config('enrol_stripepaymentpro', 'license_status'),'active') === false) {
    set_config("license_status", 'inactive', 'enrol_stripepaymentpro');
}

// If error occurs, show the error
if ($error !== null) {
    \core\notification::error($error);
}

// If success, show the success message
if ($success_message !== null) {
    \core\notification::success($success_message);
    redirect($CFG->wwwroot . '/admin/settings.php?section=enrolsettingsstripepaymentpro');
}

if ($hassiteconfig) {
    $ADMIN->add(
        'enrolments',
        new admin_externalpage(
            'enrol_stripepaymentpro/couponmanagement',
            get_string('stripepaymentpro_couponmanagement_menu_name', 'enrol_stripepaymentpro'),
            new moodle_url('/enrol/stripepaymentpro/coupons.php'),
            'moodle/site:config'
        )
    );
}

//--- general settings ----------------------------------------------------------------------------
$settings->add(new admin_setting_heading('enrol_stripepaymentpro_general_settings', new lang_string('generalsettings', 'enrol_stripepaymentpro'), ''));

$settings->add(new admin_setting_description('enrol_stripepaymentpro/stripe_settings', get_string('stripesettings', 'enrol_stripepaymentpro'), get_string('stripesettings_des', 'enrol_stripepaymentpro')));

$settings->add(new admin_setting_configtext(
    'enrol_stripepaymentpro/webservice_token',
    get_string('webservice_token_string', 'enrol_stripepaymentpro'),
    get_string('create_user_token', 'enrol_stripepaymentpro') . '<a href="' . $rest_web_link . '" target="_blank"> ' . get_string('from_here', 'enrol_stripepaymentpro') . '</a> . ' . get_string('enabled_rest_protocol', 'enrol_stripepaymentpro') . '<a href="' . $create_token . '" target="_blank"> ' . get_string('from_here', 'enrol_stripepaymentpro') . '</a>',
    ''
));

// Add a select dropdown for payment gateway type
$payment_gateway_options = array(
    'checkout' => get_string('stripe_checkout', 'enrol_stripepaymentpro'),
    'elements' => get_string('stripe_elements', 'enrol_stripepaymentpro'),
);
$settings->add(new admin_setting_configselect(
    'enrol_stripepaymentpro/payment_gateway_type',
    get_string('payment_gateway_type', 'enrol_stripepaymentpro'),
    get_string('payment_gateway_type_desc', 'enrol_stripepaymentpro'),
    'checkout', // default value
    $payment_gateway_options
));
$settings->add(new admin_setting_configcheckbox(
    'enrol_stripepaymentpro/enable_automatic_tax', 
    get_string('enable_automatic_tax', 'enrol_stripepaymentpro'), 
    '',
    0
));

// Define the URLs and link texts for your pages
$coupon_setting_url = $CFG->wwwroot . '/enrol/stripepaymentpro/coupons.php';

$coupon_setting_link_text = get_string('coupon_setting_link_text', 'enrol_stripepaymentpro'); // Make sure to define this string in your language file

// Add a link setting for coupon settings
$settings->add(new admin_setting_heading(
    'enrol_stripepaymentpro_coupon_settings',
    get_string('coupon_settings', 'enrol_stripepaymentpro'),
    get_string('coupon_settings_desc', 'enrol_stripepaymentpro') . ' <a href="' . $coupon_setting_url . '">' . $coupon_setting_link_text . '</a>'
));

//--- License settings ----------------------------------------------------------------------------
$settings->add(new admin_setting_heading('enrol_stripepaymentpro_license_settings', new lang_string('licensesetting', 'enrol_stripepaymentpro'), ''));
$settings->add(new admin_setting_configtext(
    'enrol_stripepaymentpro/apikey',
    get_string('apikey', 'enrol_stripepaymentpro'),
    '<a href="' . get_string('subscription_url', 'enrol_stripepaymentpro') . '" target="_blank">' . get_string('apikey', 'enrol_stripepaymentpro') . '</a>' . get_string('apikey_desc', 'enrol_stripepaymentpro'),
    '',
    PARAM_TEXT
));

$settings->add(new admin_setting_configtext(
    'enrol_stripepaymentpro/productid',
    get_string('productid', 'enrol_stripepaymentpro'),
    get_string('productid_desc', 'enrol_stripepaymentpro') . ' <a href="' . get_string('subscription_url', 'enrol_stripepaymentpro') . '" target="_blank">' . get_string('productid', 'enrol_stripepaymentpro') . '</a>',
    '',
    PARAM_TEXT
));

$settings->add(new admin_setting_description('enrol_stripepaymentpro/license_status', get_string('licensestatus', 'enrol_stripepaymentpro'), get_config('enrol_stripepaymentpro', 'license_status')));

if (get_config('enrol_stripepaymentpro', 'expirey') === 'When Cancelled') {
    $settings->add(new admin_setting_description('enrol_stripepaymentpro/expiry', get_string('licenseexdate', 'enrol_stripepaymentpro'), get_config('enrol_stripepaymentpro', 'expirey_day')));
} else {
    $settings->add(new admin_setting_description('enrol_stripepaymentpro/expiry', get_string('licenseexdate', 'enrol_stripepaymentpro'), get_string('expired', 'enrol_stripepaymentpro') . ' <a href="' . get_string('subscriptionrenew_url', 'enrol_stripepaymentpro') . $order_id . '" target="_blank">' . get_string('renewnow', 'enrol_stripepaymentpro') . '</a>'));
}

if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
    $settings->add(new admin_setting_description('enrol_stripepaymentpro/activate_license', '', '<button type="submit" class="btn btn-primary text-white" name="stripepaymentpro_license_set_status" value="' . get_string('deactivelicense', 'enrol_stripepaymentpro') . '">' . get_string('deactivelicense', 'enrol_stripepaymentpro') . '</button>'));
} else {
    $settings->add(new admin_setting_description('enrol_stripepaymentpro/activate_license', '', '<button type="submit" class="btn btn-primary text-white" name="stripepaymentpro_license_set_status" value="' . get_string('activelicense', 'enrol_stripepaymentpro') . '">' . get_string('activelicense', 'enrol_stripepaymentpro') . '</button>'));
}

$settings->add(new admin_setting_description('enrol_stripepaymentpro/spaceing_3', ' ', ' '));

// Code for managing the "My Subscription" menu item
if (optional_param('section', '', PARAM_TEXT) == 'enrolsettingsstripepaymentpro' ||
    stripos($_SERVER['REQUEST_URI'], 'upgradesettings.php') !== false) {

    // Custom menu items
    $currentcustommenuitems = str_replace(["\r\n", "\r"], "\n", $CFG->custommenuitems);
    $lines = explode("\n", $currentcustommenuitems);
    $lines = array_map('trim', $lines);
    $subscriptionmenu = '-My Subscription|/enrol/stripepaymentpro/mysubscription.php';

    // Add the subscription menu item if the license is active
    if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
        if (!in_array($subscriptionmenu, $lines)) {
            array_splice($lines, 1, 0, [$subscriptionmenu]);
            set_config('custommenuitems', implode("\n", $lines));
        }
    } else {
        // Remove the subscription menu item if the license is inactive
        if (in_array($subscriptionmenu, $lines)) {
            $lines = array_diff($lines, [$subscriptionmenu]);
            set_config('custommenuitems', implode("\n", $lines));
        }
    }

    // Custom user menu items
    $currentcustomusermenuitems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
    $lines = explode("\n", $currentcustomusermenuitems);
    $lines = array_map('trim', $lines);
    $subscriptionmenu = 'My Subscription|/enrol/stripepaymentpro/mysubscription.php';

    // Add the subscription user menu item if the license is active
    if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
        if (!in_array($subscriptionmenu, $lines)) {
            array_splice($lines, 1, 0, [$subscriptionmenu]);
            set_config('customusermenuitems', implode("\n", $lines));
        }
        // if there is not webhook id in config create a webhook endpoint 
        if ( !get_config('enrol_stripepaymentpro', 'stripe_webhook_id') && get_config('enrol_stripepaymentpro', 'license_status') === 'active' ) {
            $webhookcontroller = new enrol_stripepaymentpro_webhook_controller();
            $webhookcontroller->create_webhook();
        }
    } else {
        // Remove the subscription user menu item if the license is inactive
        if (in_array($subscriptionmenu, $lines)) {
            $lines = array_diff($lines, [$subscriptionmenu]);
            set_config('customusermenuitems', implode("\n", $lines));
        }
    }

}
