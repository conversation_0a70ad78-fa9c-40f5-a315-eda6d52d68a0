<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot.'/lib/adminlib.php');
require_once($CFG->dirroot.'/enrol/stripepayment/lib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');

use \Stripe\Price as Price;
use \Stripe\Stripe as Stripe;
/**
 * Stripe enrolment plugin implementation.
 * 
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_stripepaymentpro_plugin extends enrol_stripepayment_plugin {
    /**
     * Lists all renewalintarval available for plugin.
     * @return $renewalintarval
     */
    public function get_renewalintarval() {
        $times = array(
             'day', 'week', 'month', 'year'
         );
        return $times;
    }
     /**
     * Sets up navigation entries.
     *
     * @param navigation_node $instancesnode
     * @param stdClass $instance
     * @return void
     */
    public function add_course_navigation($instancesnode, stdClass $instance) {
        if ($instance->enrol !== 'stripepaymentpro') {
             throw new coding_exception('Invalid enrol instance type!');
        }
        $context = context_course::instance($instance->courseid);
        if (has_capability('enrol/stripepaymentpro:config', $context)) {
            $managelink = new moodle_url('/enrol/stripepaymentpro/edit.php',
            array('courseid' => $instance->courseid, 'id' => $instance->id));
            $instancesnode->add($this->get_instance_name($instance), $managelink, navigation_node::TYPE_SETTING);
        }
    }
    /**
     * Returns edit icons for the page with list of instances
     * @param stdClass $instance
     * @return array
     */
    public function get_action_icons(stdClass $instance) {
        global $OUTPUT;
        if ($instance->enrol !== 'stripepaymentpro') {
            throw new coding_exception('invalid enrol instance!');
        }
        $context = context_course::instance($instance->courseid);
        $icons = array();
        if (has_capability('enrol/stripepaymentpro:config', $context)) {
            $editlink = new moodle_url("/enrol/stripepaymentpro/edit.php",
            array('courseid' => $instance->courseid, 'id' => $instance->id));
            $icons[] = $OUTPUT->action_icon($editlink, new pix_icon('t/edit', get_string('edit'), 'core',
                    array('class' => 'iconsmall')));
        }
        return $icons;
    }
    /**
     * Returns link to page which may be used to add new instance of enrolment plugin in course.
     * @param int $courseid
     * @return moodle_url page url
     */
    public function get_newinstance_link($courseid) {
        $context = context_course::instance($courseid, MUST_EXIST);
        if (!has_capability('moodle/course:enrolconfig', $context) or !has_capability('enrol/stripepaymentpro:config', $context)) {
            return null;
        }
        // Multiple instances supported - different cost for different roles.
        return new moodle_url('/enrol/stripepaymentpro/edit.php', array('courseid' => $courseid));
    }
    /**
     * Creates course enrol form, checks if form submitted
     * and enrols user if necessary. It can also redirect.
     *
     * @param stdClass $instance
     * @return string html text, usually a form in a text box
     */
    public function enrol_page_hook(stdClass $instance) {
        global $CFG, $USER, $OUTPUT, $DB;
        $plugin = enrol_get_plugin('stripepaymentpro');
        Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));

        $enrolstatus = $this->can_stripepayment_enrol($instance);
        if (!$enrolstatus) {
            return get_string('maxenrolledreached', 'enrol_stripepaymentpro');
        }
        ob_start();
        if ($DB->record_exists('user_enrolments', array('userid' => $USER->id, 'enrolid' => $instance->id))) {
            return ob_get_clean();
        }
        if ($instance->enrolstartdate != 0 && $instance->enrolstartdate > time()) {
            return ob_get_clean();
        }
        if ($instance->enrolenddate != 0 && $instance->enrolenddate < time()) {
            return ob_get_clean();
        }
        $course = $DB->get_record('course', array('id' => $instance->courseid));
        $context = context_course::instance($course->id);
        $shortname = format_string($course->shortname, true, array('context' => $context));
        $strloginto = get_string("loginto", "", $shortname);
        $strcourses = get_string("courses");
        // Pass $view=true to filter hidden caps if the user cannot see them.
        if ($users = get_users_by_capability($context, 'moodle/course:update', 'u.*', 'u.id ASC',
                                             '', '', '', '', false, true)) {
            $users = sort_by_roleassignment_authority($users, $context);
            $teacher = array_shift($users);
        } else {
            $teacher = false;
        }
        $publishablekey = get_config('enrol_stripepayment', 'publishablekey');
        $cost = $instance->customtext4 > 0 ? Price::retrieve($instance->customtext4)->unit_amount/$plugin->get_fractional_unit_amount($instance->currency) : Price::retrieve($instance->customtext3)->unit_amount/$plugin->get_fractional_unit_amount($instance->currency);
        if (abs($cost) < 0.01) { // No cost, other enrolment methods (instances) should be used.
            echo '<p>'.get_string('nocost', 'enrol_stripepaymentpro').'</p>';
        } else {
            // Calculate localised and "." cost, make sure we send Stripe the same value,
            // please note Stripe expects amount with 2 decimal places and "." separator.
            $localisedcost = format_float($cost, 2, true);
            $cost = format_float($cost, 2, false);
            $productid = $instance->customtext2;
            if (isguestuser()) { // Force login only for guest user, not real users with guest role.
                if (empty($CFG->loginhttps)) {
                    $wwwroot = $CFG->wwwroot;
                } else {
                    // This actually is not so secure ;-), 'cause we're
                    // in unencrypted connection...
                    $wwwroot = str_replace("http://", "https://", $CFG->wwwroot);
                }
                echo '<div class="mdl-align"><p>'.get_string('paymentrequired').'</p>';
                echo '<p><b>'.get_string('cost').": $instance->currency $localisedcost".'</b></p>';
                echo '<p><a href="'.$wwwroot.'/login/">'.get_string('loginsite').'</a></p>';
                echo '</div>';
            } else {
                // Sanitise some fields before building the Stripe form.
                $coursefullname  = format_string($course->fullname, true, array('context' => $context));
                $courseshortname = $shortname;
                $userfullname    = fullname($USER);
                $userfirstname   = $USER->firstname;
                $userlastname    = $USER->lastname;
                $useraddress     = $USER->address;
                $usercity        = $USER->city;
                $instancename    = $this->get_instance_name($instance);
                include_once($CFG->dirroot.'/enrol/stripepaymentpro/enrol.php');
            }
        }
        return $OUTPUT->box(ob_get_clean());
    }

    /**
	 * Convert an amount in the currency's base unit to its equivalent fractional unit.
	 *
	 * Stripe wants amounts in the fractional unit (e.g., pennies), not the base unit (e.g., dollars). Zero-decimal
	 * currencies are not included yet, see `$supported_currencies`.
	 *
	 * The data here comes from https://en.wikipedia.org/wiki/List_of_circulating_currencies.
	 *
	 * @param string $order_currency
	 *
	 * @return array
	 * @throws Exception
	 */
    public function get_fractional_unit_amount( $order_currency) {
        $multiplier_amount = null;

		$currency_multipliers = array(
			5    => array( 'MRO', 'MRU' ),
			100  => array(
				'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN',
				'BMD', 'BND', 'BOB', 'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD', 'CAD', 'CDF', 'CHF', 'CNY', 'COP',
				'CRC', 'CUC', 'CUP', 'CVE', 'CZK', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'EUR', 'FJD', 'FKP',
				'GBP', 'GEL', 'GGP', 'GHS', 'GIP', 'GMD', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR',
				'ILS', 'IMP', 'INR', 'IRR', 'ISK', 'JEP', 'JMD', 'JOD', 'KES', 'KGS', 'KHR', 'KPW', 'KYD', 'KZT',
				'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'MAD', 'MDL', 'MKD', 'MMK', 'MNT', 'MOP', 'MUR', 'MVR', 'MWK',
				'MXN', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'NZD', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR',
				'PLN', 'PRB', 'QAR', 'RON', 'RSD', 'RUB', 'SAR', 'SBD', 'SCR', 'SDG', 'SEK', 'SGD', 'SHP', 'SLL',
				'SOS', 'SRD', 'SSP', 'STD', 'SYP', 'SZL', 'THB', 'TJS', 'TMT', 'TOP', 'TRY', 'TTD', 'TVD', 'TWD',
				'TZS', 'UAH', 'UGX', 'USD', 'UYU', 'UZS', 'VEF', 'WST', 'XCD', 'YER', 'ZAR', 'ZMW',
			),
			1000 => array( 'BHD', 'IQD', 'KWD', 'LYD', 'OMR', 'TND' ),
		);

		foreach ( $currency_multipliers as $multiplier => $currencies ) {
			if ( in_array( $order_currency, $currencies, true ) ) {
                $multiplier_amount = $multiplier;
			}
		}

		if ( is_null( $multiplier_amount ) ) {
			throw new Exception( "Unknown currency multiplier for $order_currency." );
		}

		return  $multiplier_amount;
	}

    /**
     * get the coupon details and create the discount price object 
     * 
     * @param int $amount the initial amount for which the the discount will deduct
     * @param int $couponid in the coupon id getting from stripe  
     */
    public function create_discounted_price_object($discountedpriceobject, $amount, $coupon, $currency, $enrol) {
        $plugin = enrol_get_plugin('stripepaymentpro');
        // If there is one time cost then apply to the one time fees
        if($amount > 0){
            try {
                // Calculate the discounted amount
                if ($coupon->percent_off) {
                    $discounted_amount = $amount * (1 - $coupon->percent_off / 100);
                } elseif ($coupon->amount_off) {
                    $discounted_amount = $amount - $coupon->amount_off;
                } else {
                    $discounted_amount = $amount;
                }
                $discountedpriceobject =  Price::create([
                'unit_amount' => $discounted_amount,
                'currency' => $currency,
                'product'    => $enrol->customtext2,
                ]);

            } catch (Exception $e) {
                \core\notification::error($e->getMessage());
            }
        }
        return $discountedpriceobject;
    }
}
