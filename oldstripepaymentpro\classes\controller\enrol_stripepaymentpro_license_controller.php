<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/filelib.php');
if (!class_exists('enrol_stripepaymentpro_license_controller')) {
	/**
 	 * Stripe enrolment plugin.
 	 *
 	 * license controller for activate deactivate and check license status 
 	 *
 	 * @package    enrol_stripepaymentpro
 	 * <AUTHOR> <<EMAIL>>
 	 * @copyright  2023 DualCube Team(https://dualcube.com)
 	 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 	 */
    class enrol_stripepaymentpro_license_controller{
        /**
         *
         * @var string api key
         */
        private $licensekey = '';
        /**
         *
         * @var string subscriptionid
         */
        private $subscriptionid = '';
        /**
         *
         * @var string pluginshortname
         */
        private $pluginshortname = '';

        /**
         *
         * @var string productid
         */
        private $productid = '';
        /**
         *
         * @var string stores the current plugin version
         */
        private $pluginversion = '';
        /**
         *
         * @var string  Stores the URL of store. Retrieves updates from
         *              this store
         */
        private $storeurl = '';
        /**
         *
         * @var string  plugin name
         */
        private $plugin  = '';

        /**
         * Developer Note: This variable is used everywhere to check license information and verify the data.
         * Change the Name of this variable in this file wherever it appears and also remove this comment
         * After you are done with adding Licensing
         * And String to add- 'noactivationremain' , 'noapi' ,
         */
        public $license_auth_data = array(
            'pluginshortname' => 'stripepaymentpro', // This short name is the plugin directory name
            'pluginversion' => '1.0.2', // Current Version of the plugin. This should be similar to Version tag mentioned in Plugin headers
            'storeurl' => 'https://dualcube.com/', // Url where program pings to check if update is available and license validity
        );

        private $webhookcontroller;

        /**
         *
         * constructor
         */
        public function __construct() {
            $this->pluginshortname = $this->license_auth_data['pluginshortname'];
            $this->pluginversion    = $this->license_auth_data['pluginversion'];
            $this->storeurl         = $this->license_auth_data['storeurl'];
            $this->plugin = enrol_get_plugin($this->pluginshortname);
            $this->licensekey = get_config('enrol_stripepaymentpro', 'apikey');
            $this->subscriptionid = get_config('enrol_stripepaymentpro', 'subscriptionid');
            $this->productid = get_config('enrol_stripepaymentpro', 'productid');
            $this->webhookcontroller = new enrol_stripepaymentpro_webhook_controller();
        }
    
    	/**
    	 * function for active license 
    	 */
        public function activate_license() {
            global $CFG;
            $requesturl = $this->storeurl;
            $postfields = array(
                'wc-api' => 'wc-am-api',
                'wc_am_action' => 'activate',
                'instance' => $this->subscriptionid,
                'product_id' => $this->productid,
                'api_key' => $this->licensekey,
                'version' => $this->pluginversion,
                'object' => $CFG->wwwroot,
            );
            $curl = new \curl();
            $options = [
                'CURLOPT_RETURNTRANSFER' => true,
                'CURLOPT_TIMEOUT' => 30,
                'CURLOPT_USERAGENT' => $_SERVER['HTTP_USER_AGENT'] . ' - ' . $CFG->wwwroot,
                'CURLOPT_SSL_VERIFYPEER' => false,
            ];
            $resp = $curl->post($requesturl, $postfields, $options);
            $licensedata = json_decode($resp);
            return $licensedata;
        }
    
    	/**
    	 * function for deactivate license for pro
    	 */ 
        public function deactivate_license() {
            global $CFG;
            if (!empty($this->licensekey)) {
                $requesturl = $this->storeurl;
                $postfields = [
                    'wc-api' => 'wc-am-api',
                    'wc_am_action' => 'deactivate',
                    'instance' => $this->subscriptionid,
                    'product_id' => $this->productid,
                    'api_key' => $this->licensekey,
                    'version' => $this->pluginversion,
                    'object' => $CFG->wwwroot,
                ];

                $curl = new \curl();
                $options = [
                    'CURLOPT_RETURNTRANSFER' => true,
                    'CURLOPT_TIMEOUT' => 30,
                    'CURLOPT_USERAGENT' => $_SERVER['HTTP_USER_AGENT'] . ' - ' . $CFG->wwwroot,
                    'CURLOPT_SSL_VERIFYPEER' => false,
                ];
                $resp = $curl->post($requesturl, $postfields, $options);
                $licensedata = json_decode($resp);
                return $licensedata;
            }
        }
    
    	/**
    	 * main function for handeling the activation and deactivation of license
    	 * we are creating and destroying webhookendpoint when license is activated and deactivated
    	 *
    	 */
        public function add_data() {
            if (!is_siteadmin()) {
            	\core\notification::error(get_string('permissionerror', 'enrol_stripepaymentpro'));
            	return;
            }
            $licensestatus = self::get_status_from_api(true);
        	// If licensestatus->success is false means there is some problem in fetching license status from get_status_from_api().
            if (!$licensestatus->success) {
                return $licensestatus;
            }
        	// activate  license on request of activate license and also checks that currently the license is deactivated
            if (optional_param('stripepaymentpro_license_set_status', '', PARAM_RAW) === 'Activate License' ) {
                if (get_config('enrol_stripepaymentpro', 'license_status') === 'inactive' && $licensestatus->data->activations_remaining > 0) {
                    $activation = $this->activate_license();
                    if(isset($activation->code) && $activation->code == 100) {
                        return $activation;
                    }
                	if ($activation->activated) {
                        set_config("license_status", 'active', 'enrol_stripepaymentpro');
                    }

                	// if there is not webhook id in config create a webhook endpoint 
                    if ( !get_config('enrol_stripepaymentpro', 'stripe_webhook_id') && get_config('enrol_stripepaymentpro', 'license_status') === 'active' ) {
                        $this->webhookcontroller->create_webhook();
                    }
                    return $activation;
                }
            } else if (optional_param('stripepaymentpro_license_set_status', '', PARAM_RAW) === 'Deactivate License' ) {
            	// deactivate license if the license is active and has request to dective license
                if ( get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
                    $deactivation = $this->deactivate_license();
                    if(isset($deactivation->code) && $deactivation->code == 100) {
                        return $deactivation;
                    }
                    set_config("license_status", 'inactive', 'enrol_stripepaymentpro');
                	
                	// while deactivating the license we are deleting the web hook endpoint
                    if ( $webhookid = get_config('enrol_stripepaymentpro', 'stripe_webhook_id')) {
                        $this->webhookcontroller->delete_webhook($webhookid);
                    }
                    return $deactivation;
                }
            }
        }
    
    	/**
    	 * function for getting status from api manager 
    	 */
        public function get_status_from_api($is_trying_activation = false) {
            global $CFG;
            if ($this->subscriptionid == null) {
                $this->subscriptionid = '0000';
            }
            $requesturl = $this->storeurl;
            $postfields = [
                'wc-api' => 'wc-am-api',
                'wc_am_action' => 'status',
                'instance' => $this->subscriptionid,
                'product_id' => $this->productid,
                'api_key' => $this->licensekey,
                'version' => $this->pluginversion,
                'object' => $CFG->wwwroot,
            ];

            $curl = new \curl();
            $options = [
                'CURLOPT_RETURNTRANSFER' => true,
                'CURLOPT_TIMEOUT' => 30,
                'CURLOPT_USERAGENT' => $_SERVER['HTTP_USER_AGENT'] . ' - ' . $CFG->wwwroot,
                'CURLOPT_SSL_VERIFYPEER' => false,
            ];
            $resp = $curl->post($requesturl, $postfields, $options);
        
            $licensedata = json_decode($resp);
            if(isset($licensedata->code) && $licensedata->code == 100) {
                return $licensedata;
            }
            $expirey = $licensedata->data->api_key_expirations->wc_subs_resources[0]->friendly_api_key_expiration_date ?? null;
            $expirey_day = $licensedata->data->api_key_expirations->wc_subs_resources[0]->next_payment;

            if ($expirey === null) {
                $expirey = get_string('expired', 'enrol_stripepaymentpro');
            }
            // Set up the expiry status into settings
            set_config('expirey', $expirey, 'enrol_stripepaymentpro');
            //set up in which day licence will be expire
            set_config('expirey_day', $expirey_day, 'enrol_stripepaymentpro');
            if(!$is_trying_activation) {
                //set license status
                set_config('license_status', $licensedata->status_check, 'enrol_stripepaymentpro');
            }

            if ($this->subscriptionid != $licensedata->data->api_key_expirations->wc_subs_resources[0]->sub_id) {
                set_config('subscriptionid', $licensedata->data->api_key_expirations->wc_subs_resources[0]->sub_id, 'enrol_stripepaymentpro');

            }
            return $licensedata;
        }
    }
}
