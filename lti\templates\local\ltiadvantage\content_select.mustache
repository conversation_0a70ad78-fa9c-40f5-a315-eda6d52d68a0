{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_lti/local/ltiadvantage/content_select

    Template which displays a list of published courses and activities.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * hascontent
    * action
    * launchid
    * sesskey
    * courses

    Example context (json):
    {
        "hascontent": true,
        "action": "https://example.com/enrol/lti/configure.php",
        "launchid": "123-456-789",
        "sesskey": "123456789",
        "courses": [
            {
                "id": 24,
                "fullname": "Introduction to chemistry",
                "shared_course": false,
                "modules": [
                    {
                        "id": 45,
                        "name": "Quiz 1",
                        "lineitem": true
                    }
                ]
            },
            {
                "id": 67,
                "fullname": "Programming 101",
                "shared_course": true
            }
        ]
    }
}}
<div id="lti_content_select">
    <h1>{{#str}}publishedcontent, enrol_lti{{/str}}</h1>
    {{#hascontent}}
        <form action="{{action}}" method="POST">
            <input type="hidden" name="launchid" value="{{launchid}}">
            <input type="hidden" name="sesskey" value="{{sesskey}}">
            <table class="table" aria-label="{{#str}}publishedcontent, enrol_lti{{/str}}">
                <colgroup>
                    <col class="w-50">
                    <col class="w-25">
                    <col class="w-25">
                </colgroup>
                <thead>
                    <tr>
                        <th class="header" scope="col"></th>
                        <th class="header" scope="col">{{#str}}addtocourse, enrol_lti{{/str}}</th>
                        <th class="header" scope="col">{{#str}}addtogradebook, enrol_lti{{/str}}</th>
                    </tr>
                </thead>
                <tbody>
                    {{#courses}}
                        <tr>
                            <td>
                                {{fullname}}
                            </td>
                            <td>
                                {{#shared_course}}
                                    <input type="checkbox" name="modules[]" value="{{id}}">
                                {{/shared_course}}
                            </td>
                            <td>
                                {{#shared_course}}
                                    <input type="checkbox" name="grades[]" value="{{id}}">
                                {{/shared_course}}
                            </td>
                        </tr>
                        {{#modules}}
                            <tr>
                                <td>{{name}}</td>
                                <td><input type="checkbox" name="modules[]" value="{{id}}"></td>
                                {{#lineitem}}
                                <td><input type="checkbox" name="grades[]" value="{{id}}"></td>
                                {{/lineitem}}
                                {{^lineitem}}
                                <td>-</td>
                                {{/lineitem}}
                            </tr>
                        {{/modules}}
                    {{/courses}}
                </tbody>
            </table>
            <input type="submit" class="btn btn-primary" value="{{#str}}addcontent, enrol_lti{{/str}}">
        </form>
    {{/hascontent}}
    {{^hascontent}}
        {{#str}}nopublishedcontent, enrol_lti{{/str}}
    {{/hascontent}}
</div>
{{#js}}
    require(['enrol_lti/content_select'], function(ContentSelect) {
        ContentSelect.init();
    });
{{/js}}
