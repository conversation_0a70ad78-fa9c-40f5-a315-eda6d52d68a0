{"version": 3, "file": "content_select.min.js", "sources": ["../src/content_select.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n/**\n * Module providing checkbox autoselection behaviour to the table on the select content deep linking view, launch_deeplink.php.\n *\n * @module     enrol_lti/content_select\n * @copyright  2021 Jake <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n// Register the checkbox change events allowing the automatic selection/deselection of the\n// 'add to gradebook' and 'add to course' checkboxes when selecting an activity/resource.\nconst registerEventHandlers = () => {\n    document.addEventListener('change', e => {\n        if (e.target.matches(\"input[type='checkbox'][name^='modules']\")) {\n            const value = e.target.value;\n            const gradecheckbox = document.querySelector(\"input[type='checkbox'][name^='grades'][value='\" + value + \"']\");\n            if (gradecheckbox) {\n                gradecheckbox.checked = e.target.checked;\n            }\n        }\n\n        if (e.target.matches(\"input[type='checkbox'][name^='grades']\")) {\n            const value = e.target.value;\n            const modcheckbox = document.querySelector(\"input[type='checkbox'][name^='modules'][value='\" + value + \"']\");\n            if (e.target.checked) {\n                modcheckbox.checked = true;\n            }\n        }\n    });\n};\n\nexport const init = () => {\n    registerEventHandlers();\n};\n"], "names": ["document", "addEventListener", "e", "target", "matches", "value", "gradecheckbox", "querySelector", "checked", "modcheckbox"], "mappings": "6JA2CoB,KAnBhBA,SAASC,iBAAiB,UAAUC,OAC5BA,EAAEC,OAAOC,QAAQ,2CAA4C,OACvDC,MAAQH,EAAEC,OAAOE,MACjBC,cAAgBN,SAASO,cAAc,iDAAmDF,MAAQ,MACpGC,gBACAA,cAAcE,QAAUN,EAAEC,OAAOK,YAIrCN,EAAEC,OAAOC,QAAQ,0CAA2C,OACtDC,MAAQH,EAAEC,OAAOE,MACjBI,YAAcT,SAASO,cAAc,kDAAoDF,MAAQ,MACnGH,EAAEC,OAAOK,UACTC,YAAYD,SAAU"}