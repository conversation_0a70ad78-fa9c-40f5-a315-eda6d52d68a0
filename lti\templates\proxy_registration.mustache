{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_lti/proxy_registration

    The content to display when editing a tool.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * returnurl The url to return the page to. Can be null if no return url avaliable.

    Example context (json):
    {}
}}
{{#str}} successfulregistration, enrol_lti {{/str}}
<br/>
{{#returnurl}}
<a href="{{{returnurl}}}">{{#str}} continue {{/str}}</a>
{{/returnurl}}
