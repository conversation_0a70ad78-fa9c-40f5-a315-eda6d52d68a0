{"version": 3, "file": "form-potential-user-selector.min.js", "sources": ["../src/form-potential-user-selector.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Potential user selector module.\n *\n * @module     enrol_manual/form-potential-user-selector\n * @copyright  2016 Damyon Wiese\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\ndefine(['jquery', 'core/ajax', 'core/templates', 'core/str'], function($, Ajax, Templates, Str) {\n\n    return /** @alias module:enrol_manual/form-potential-user-selector */ {\n\n        processResults: function(selector, results) {\n            var users = [];\n            if ($.isArray(results)) {\n                $.each(results, function(index, user) {\n                    users.push({\n                        value: user.id,\n                        label: user._label\n                    });\n                });\n                return users;\n\n            } else {\n                return results;\n            }\n        },\n\n        transport: function(selector, query, success, failure) {\n            var promise;\n            var courseid = $(selector).attr('courseid');\n            var userfields = $(selector).attr('userfields').split(',');\n            if (typeof courseid === \"undefined\") {\n                courseid = '1';\n            }\n            var enrolid = $(selector).attr('enrolid');\n            if (typeof enrolid === \"undefined\") {\n                enrolid = '';\n            }\n            var perpage = parseInt($(selector).attr('perpage'));\n            if (isNaN(perpage)) {\n                perpage = 100;\n            }\n\n            promise = Ajax.call([{\n                methodname: 'core_enrol_get_potential_users',\n                args: {\n                    courseid: courseid,\n                    enrolid: enrolid,\n                    search: query,\n                    searchanywhere: true,\n                    page: 0,\n                    perpage: perpage + 1\n                }\n            }]);\n\n            promise[0].then(function(results) {\n                var promises = [],\n                    i = 0;\n\n                if (results.length <= perpage) {\n                    // Render the label.\n                    const profileRegex = /^profile_field_(.*)$/;\n                    $.each(results, function(index, user) {\n                        var ctx = user,\n                            identity = [];\n                        $.each(userfields, function(i, k) {\n                            const result = profileRegex.exec(k);\n                            if (result) {\n                                if (user.customfields) {\n                                    user.customfields.forEach(function(customfield) {\n                                        if (customfield.shortname === result[1]) {\n                                            ctx.hasidentity = true;\n                                            identity.push(customfield.value);\n                                        }\n\n                                    });\n                                }\n                            } else {\n                                if (typeof user[k] !== 'undefined' && user[k] !== '') {\n                                    ctx.hasidentity = true;\n                                    identity.push(user[k]);\n                                }\n                            }\n                        });\n                        ctx.identity = identity.join(', ');\n                        promises.push(Templates.render('enrol_manual/form-user-selector-suggestion', ctx));\n                    });\n\n                    // Apply the label to the results.\n                    return $.when.apply($.when, promises).then(function() {\n                        var args = arguments;\n                        $.each(results, function(index, user) {\n                            user._label = args[i];\n                            i++;\n                        });\n                        success(results);\n                        return;\n                    });\n\n                } else {\n                    return Str.get_string('toomanyuserstoshow', 'core', '>' + perpage).then(function(toomanyuserstoshow) {\n                        success(toomanyuserstoshow);\n                        return;\n                    });\n                }\n\n            }).fail(failure);\n        }\n\n    };\n\n});\n"], "names": ["define", "$", "Ajax", "Templates", "Str", "processResults", "selector", "results", "users", "isArray", "each", "index", "user", "push", "value", "id", "label", "_label", "transport", "query", "success", "failure", "courseid", "attr", "userfields", "split", "enrolid", "perpage", "parseInt", "isNaN", "call", "methodname", "args", "search", "searchanywhere", "page", "then", "promises", "i", "length", "profileRegex", "ctx", "identity", "k", "result", "exec", "customfields", "for<PERSON>ach", "customfield", "shortname", "hasidentity", "join", "render", "when", "apply", "arguments", "get_string", "toomanyuserstoshow", "fail"], "mappings": ";;;;;;;AAuBAA,mDAAO,CAAC,SAAU,YAAa,iBAAkB,aAAa,SAASC,EAAGC,KAAMC,UAAWC,WAEjB,CAElEC,eAAgB,SAASC,SAAUC,aAC3BC,MAAQ,UACRP,EAAEQ,QAAQF,UACVN,EAAES,KAAKH,SAAS,SAASI,MAAOC,MAC5BJ,MAAMK,KAAK,CACPC,MAAOF,KAAKG,GACZC,MAAOJ,KAAKK,YAGbT,OAGAD,SAIfW,UAAW,SAASZ,SAAUa,MAAOC,QAASC,aAEtCC,SAAWrB,EAAEK,UAAUiB,KAAK,YAC5BC,WAAavB,EAAEK,UAAUiB,KAAK,cAAcE,MAAM,UAC9B,IAAbH,WACPA,SAAW,SAEXI,QAAUzB,EAAEK,UAAUiB,KAAK,gBACR,IAAZG,UACPA,QAAU,QAEVC,QAAUC,SAAS3B,EAAEK,UAAUiB,KAAK,YACpCM,MAAMF,WACNA,QAAU,KAGJzB,KAAK4B,KAAK,CAAC,CACjBC,WAAY,iCACZC,KAAM,CACFV,SAAUA,SACVI,QAASA,QACTO,OAAQd,MACRe,gBAAgB,EAChBC,KAAM,EACNR,QAASA,QAAU,MAInB,GAAGS,MAAK,SAAS7B,aACjB8B,SAAW,GACXC,EAAI,KAEJ/B,QAAQgC,QAAUZ,QAAS,OAErBa,aAAe,8BACrBvC,EAAES,KAAKH,SAAS,SAASI,MAAOC,UACxB6B,IAAM7B,KACN8B,SAAW,GACfzC,EAAES,KAAKc,YAAY,SAASc,EAAGK,SACrBC,OAASJ,aAAaK,KAAKF,GAC7BC,OACIhC,KAAKkC,cACLlC,KAAKkC,aAAaC,SAAQ,SAASC,aAC3BA,YAAYC,YAAcL,OAAO,KACjCH,IAAIS,aAAc,EAClBR,SAAS7B,KAAKmC,YAAYlC,gBAMf,IAAZF,KAAK+B,IAAkC,KAAZ/B,KAAK+B,KACvCF,IAAIS,aAAc,EAClBR,SAAS7B,KAAKD,KAAK+B,QAI/BF,IAAIC,SAAWA,SAASS,KAAK,MAC7Bd,SAASxB,KAAKV,UAAUiD,OAAO,6CAA8CX,SAI1ExC,EAAEoD,KAAKC,MAAMrD,EAAEoD,KAAMhB,UAAUD,MAAK,eACnCJ,KAAOuB,UACXtD,EAAES,KAAKH,SAAS,SAASI,MAAOC,MAC5BA,KAAKK,OAASe,KAAKM,GACnBA,OAEJlB,QAAQb,mBAKLH,IAAIoD,WAAW,qBAAsB,OAAQ,IAAM7B,SAASS,MAAK,SAASqB,oBAC7ErC,QAAQqC,0BAKjBC,KAAKrC"}