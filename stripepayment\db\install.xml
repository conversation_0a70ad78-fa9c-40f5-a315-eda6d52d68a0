<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="enrol/stripepayment/db" VERSION="20120122" COMMENT="XMLDB file for Moodle enrol/stripe" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
  <TABLES>
    <TABLE NAME="enrol_stripepayment" COMMENT="Holds all known information about Stripe transactions">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" />
        <FIELD NAME="receiver_email" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="receiver_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="item_name" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="coupon_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="memo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="price" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="payment_status" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="pending_reason" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="reason_code" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="txn_id" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="payment_type" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="timeupdated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id" />
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>