<?php

// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../../config.php');
require_once('edit_form.php');
require_once('lib.php');
require_once($CFG->libdir . '/enrollib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');


use \Stripe\Stripe as Stripe;
use \Stripe\Price as Price;
use \Stripe\Product as Product;
use \stripe\Plan as Plan;

$courseid   = required_param('courseid', PARAM_INT);
$instanceid = optional_param('id', 0, PARAM_INT); // Instanceid.

$course = $DB->get_record('course', array('id' => $courseid), '*', MUST_EXIST);
$context = context_course::instance($course->id, MUST_EXIST);

$plugincore = enrol_get_plugin('stripepayment');
$plugin = enrol_get_plugin('stripepaymentpro');

require_login($course);
require_capability('enrol/stripepaymentpro:config', $context);

$PAGE->set_url('/enrol/stripepaymentpro/edit.php', array('courseid' => $course->id, 'id' => $instanceid));
$PAGE->set_pagelayout('admin');

$return = new moodle_url('/enrol/instances.php', array('id' => $course->id));
if (!enrol_is_enabled('stripepaymentpro')) {
    redirect($return);
}

Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));

if ($instanceid) {
    $instance = $DB->get_record(
        'enrol',
        array('courseid' => $course->id, 'enrol' => 'stripepaymentpro', 'id' => $instanceid),
        '*',
        MUST_EXIST
    );
    $instance->cost = format_float($instance->cost, 2, true);
} else {
    require_capability('moodle/course:enrolconfig', $context);
    // No instance yet, we have to add new instance.
    navigation_node::override_active_url(new moodle_url('/enrol/instances.php', array('id' => $course->id)));
    $instance = new stdClass();
    $instance->id       = null;
    $instance->courseid = $course->id;
}

$mform = new enrol_stripepaymentpro_edit_form(null, array($instance, $plugin, $plugincore, $context));

if ($mform->is_cancelled()) {
    redirect($return);
} else if ($data = $mform->get_data()) {
    $initial_amount = $data->cost * $plugin->get_fractional_unit_amount($data->currency);
    
	 // edit details in edit.php fo this there shpuld be instance id 
    if ($instance->id) {
            $product = Product::retrieve($instance->customtext2);

            // Create or update initial price (one-time charge)
            $initial_price = Price::create([
                'unit_amount' => $initial_amount,
                'currency' => $data->currency,
                'product' => $product->id,
            ]);

        if(isset($data->recurringproduct)) {
            $recurring_amount = $data->recurringcost * $plugin->get_fractional_unit_amount($data->currency);
            $interval = $plugin->get_renewalintarval();

            // Create or update recurring price
            $recurring_price = Price::create([
                'unit_amount' => $recurring_amount,
                'currency' => $data->currency,
                'recurring' => [
                    //'trial_period_days' => $data->customint2 / 86400,
                    'interval' => $interval[$data->customint4],
                    'interval_count' => $data->customint1,
                ],
                'product' => $product->id,
            ]);
        }
            

            // Update product with new prices
            Product::update($product->id, [
                'default_price' => $initial_price->id,
            ]);
		
        // Update instance fields
        $instance->status = $data->status;
        $instance->cost = unformat_float($data->cost);
        $instance->currency = $data->currency;
        $instance->roleid = $data->roleid;
        $instance->customint3 = $data->customint3;
        $instance->enrolperiod = $data->enrolperiod;
        $instance->enrolstartdate = $data->enrolstartdate;
        $instance->enrolenddate = $data->enrolenddate;
        $instance->timemodified = time();
        $instance->customtext3 = $initial_price->id; // Initial price ID
        $instance->customtext4 = isset($recurring_price) ? $recurring_price->id : 0; // Recurring price ID
        $instance->customtext1 = isset($interval) ? $interval[$data->customint4] : 0; // Renewal Interval
        $instance->customint4 = $data->customint4; // Renewal Interval save
        $instance->customint1 = $data->customint1; // Renewal Interval Period
        $instance->customint2 = $data->customint2; // Trial Period

        $DB->update_record('enrol', $instance);

        if ($reset = ($instance->status != $data->status)) {
            $context->mark_dirty();
        }
    } else {
    // create stripe payment pro instance for first time
        $product = Product::create([
            'name' => $course->fullname,
            'metadata' => [
                'id' => $course->id,
            ],
        ]);

        $initial_price = Price::create([
            'unit_amount' => $initial_amount,
            'currency' => $data->currency,
            'product' => $product->id,
        ]);

        if(isset($data->recurringproduct)) {
            $recurring_amount = $data->recurringcost * $plugin->get_fractional_unit_amount($data->currency);
            $interval = $plugin->get_renewalintarval();


            $recurring_price = Price::create([
                'unit_amount' => $recurring_amount,
                'currency' => $data->currency,
                'recurring' => [
                    'interval' => $interval[$data->customint4],
                    'interval_count' => $data->customint1,
                    //'trial_period_days' => $data->customint2 ? $data->customint2/86400 : 0,
                ],
                'product' => $product->id,
            ]);
        }

        Product::update($product->id, [
            'default_price' => $initial_price->id,
        ]);

        $fields = array(
            'status' => $data->status,
            //'name' => $data->name,
            'cost' => unformat_float($data->cost),
            'recurringcost' => unformat_float($data->recurringcost),
            'currency' => $data->currency,
            'roleid' => $data->roleid,
            'enrolperiod' => $data->enrolperiod,
            'customint3' => $data->customint3,
            'enrolstartdate' => $data->enrolstartdate,
            'enrolenddate' => $data->enrolenddate,
            'customtext2' => $product->id, // Product ID
            'customtext3' => $initial_price->id, // Initial price ID
            'customtext4' => isset($recurring_price) ? $recurring_price->id : 0, // Recurring price ID
            'customtext1' => isset($interval) ? $interval[$data->customint4] : 0, // Renewal Interval
            'customint4' => isset($data->customint4) ? $data->customint4 : 0, // Renewal Interval save
            'customint1' => $data->customint1, // Renewal Interval Period
            'customint2' => $data->customint2, // Trial Period
        );

		// if we add instance to our plugin it will insert record in enrol table as a instance
        $plugin->add_instance($course, $fields);
    }

    redirect($return);
}

$PAGE->set_heading($course->fullname);
$PAGE->set_title(get_string('pluginname', 'enrol_stripepaymentpro'));

echo $OUTPUT->header();
echo $OUTPUT->heading(get_string('pluginname', 'enrol_stripepaymentpro'));
$mform->display();
echo $OUTPUT->footer();
