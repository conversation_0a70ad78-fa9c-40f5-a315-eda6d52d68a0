define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default,r=(e,t,n)=>o([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0],s=e=>{const t=new Map;return{getelement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setelement(e,t){const n=this.getelement(e);n&&(n.innerHTML=t)},toggleelement(e,t){const n=this.getelement(e);n&&(n.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,n,o=(t?"0.7":"1")){const r=this.getelement(e);r&&(r.disabled=t,r.textContent=n,r.style.opacity=o,r.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment_pro:function(e,t,n,a,l,i,c,u,p){const m=s(n);let d,g=!1;const y=()=>{if(d&&d.checkout&&"function"==typeof d.checkout.destroy)try{d.checkout.destroy()}catch(e){console.warn("Error destroying checkout:",e)}d=null,g=!1};if(void 0===window.Stripe)return f("paymentresponse","Stripe.js library not loaded. Please check your template includes.","error"),void console.error("Stripe.js not loaded.");const f=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}m.setelement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),m.toggleelement(e,!0)},h=e=>{m.setelement(e,""),m.toggleelement(e,!1)},w=e=>{if(console.log("Updating UI from server response:",e),console.log("Current payment gateway type:",p),console.log("Is initialized:",g),e.message?f("showmessage",e.message,"error"===e.uistate?"error":"success"):h("showmessage"),"elements"===p?(console.log("Elements mode - UI state:",e.uistate),"paid"===e.uistate?(console.log("Course is free, showing enrol button"),m.toggleelement("enrolbutton",!0),m.toggleelement("buynow",!0),m.toggleelement("load-payment-form",!1)):(console.log("Course requires payment, keeping Elements UI"),m.toggleelement("enrolbutton",!1),m.toggleelement("buynow",!1))):(console.log("Checkout mode - UI state:",e.uistate),m.toggleelement("enrolbutton","paid"===e.uistate||"discount"===e.uistate),m.toggleelement("buynow","paid"===e.uistate||"discount"===e.uistate)),m.toggleelement("total","paid"===e.uistate||"discount"===e.uistate),"error"!==e.uistate){if(m.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection){if(e.couponname&&m.setelement("discounttag",e.couponname),e.discountamount){const t="USD"===e.currency?"$":e.currency+" ";m.setelement("discountamountdisplay",`-${t}${e.discountamount}`)}if(e.discountamount&&e.discountvalue){const t="USD"===e.currency?"$":e.currency+" ";let n="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${t}${e.discountvalue} off`;e.couponduration&&("repeating"===e.couponduration&&e.coupondurationmonths?n+=` Expires in ${e.coupondurationmonths} months`:"once"!==e.couponduration&&(n+=` ${e.couponduration}`)),m.setelement("discountnote",n)}}if(e.status&&e.currency){const t="USD"===e.currency?"$":e.currency+" ",n=`${t}${parseFloat(e.status).toFixed(2)}`,o=m.getelement("mainprice");o&&(o.textContent=n);const r=m.getelement("totalamount");if(r&&(r.textContent=n),void 0!==e.discounted_renewal_fee&&void 0!==e.original_renewal_fee){const n=parseFloat(e.discounted_renewal_fee),o=parseFloat(e.original_renewal_fee);if(n!==o){console.log(`Updating recurring price from ${o} to ${n}`);[m.getelement("recurring-price-heading"),m.getelement("recurring-price-breakdown")].forEach(e=>{e&&e.textContent.includes(t+o.toFixed(2))&&(e.textContent=e.textContent.replace(t+o.toFixed(2),t+n.toFixed(2)),console.log(`Updated recurring price element: ${e.textContent}`))});document.querySelectorAll("*").forEach(e=>{e.textContent&&e.textContent.includes("Then "+t+o.toFixed(2))&&(e.textContent=e.textContent.replace("Then "+t+o.toFixed(2),"Then "+t+n.toFixed(2)),console.log(`Updated "Then" text: ${e.textContent}`))})}}"elements"===p&&(console.log("Handling Elements mode after coupon, state:",e.uistate),"paid"===e.uistate?g&&y():"discount"!==e.uistate&&"error"!==e.uistate||(g?(console.log("Payment form loaded, refreshing with coupon"),(async()=>{if(g){console.log("Refreshing payment element after coupon application...");try{if(f("paymentresponse","Updating payment form with coupon discount...","info"),y(),!x)throw new Error("Elements initialization function not available");await x(),h("paymentresponse")}catch(e){console.error("Error refreshing payment element:",e),f("paymentresponse","Failed to update payment form. Please reload the page.","error")}}else console.log("Payment element not initialized, skipping refresh")})()):(console.log("Payment form not loaded, showing load button"),m.toggleelement("load-payment-form",!0))))}}},b=async e=>{e.preventDefault();const r=m.getelement("coupon"),s=r?.value.trim();if(!s)return f("showmessage",i,"error"),void m.focuselement("coupon");m.setbutton("apply",!0,c);try{const e=await((e,t)=>o([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(s,n);if(console.log("Coupon application response:",e),console.log("Current payment gateway type:",p),void 0===e?.status)throw new Error("Invalid server response");t=s,m.toggleelement("coupon",!1),m.toggleelement("apply",!1),w(e)}catch(e){console.error("Coupon application error:",e),f("showmessage",e.message||"Coupon validation failed","error"),m.focuselement("coupon")}finally{m.setbutton("apply",!1,"Apply")}},v=async()=>{if(m.getelement("enrolbutton")){h("paymentresponse"),m.setbutton("enrolbutton",!0,l);try{const o=await r(e,t,n);o.error?.message?f("paymentresponse",o.error.message,"error"):"success"===o.status&&o.redirecturl?window.location.href=o.redirecturl:f("paymentresponse","Unknown error occurred during payment.","error")}catch(e){f("paymentresponse",e.message,"error")}finally{m.toggleelement("enrolbutton",!1)}}};let x;const C=async()=>{if(m.getelement("load-payment-button")){m.setbutton("load-payment-button",!0,"Loading...");try{if(!x)throw new Error("Elements initialization function not available");await x(),m.toggleelement("load-payment-form",!1)}catch(e){console.error("Error loading payment form:",e),f("paymentresponse","Failed to load payment form. Please try again.","error"),m.setbutton("load-payment-button",!1,"Load Payment Form")}}};if("elements"===p){m.toggleelement("enrolbutton",!1),m.toggleelement("buynow",!1),m.toggleelement("load-payment-form",!0);const o=window.Stripe(a);x=async()=>{if(g)return void console.log("Embedded checkout already initialized, skipping...");y();const s=m.getelement("payment-element");if(s){h("paymentresponse");try{const a=async()=>{const o=await r(e,t,n);if(console.log("Stripe enrol response:",o),o.error&&o.error.message)throw new Error(o.error.message);if(o.paymentintent){console.log("PaymentIntent data:",o.paymentintent);try{const e="string"==typeof o.paymentintent?JSON.parse(o.paymentintent):o.paymentintent;return console.log("Extracted client_secret:",e.client_secret),e.client_secret}catch(e){throw console.error("Failed to parse paymentintent:",e),console.error("PaymentIntent raw data:",o.paymentintent),new Error("Invalid payment data received from server.")}}throw new Error("No client secret found in response")};s.innerHTML="";const l=await o.initEmbeddedCheckout({fetchClientSecret:a});l.mount(`#${s.id}`),d={stripe:o,checkout:l},g=!0,m.toggleelement("payment-element",!0),m.toggleelement("buynow",!1)}catch(e){console.error("Stripe Elements initialization error:",e),f("paymentresponse",e.message||"Stripe initialization error. Check console.","error"),g=!1}}else f("paymentresponse","Payment element container (ID: payment-element) not found in HTML. Check your template.","error")}}else m.toggleelement("enrolbutton",!0),m.setbutton("enrolbutton",!1,"Buy Now"),m.toggleelement("payment-element",!1),m.toggleelement("buynow",!0);[{id:"apply",event:"click",handler:b},{id:"enrolbutton",event:"click",handler:v},{id:"load-payment-button",event:"click",handler:C}].forEach(({id:e,event:t,handler:n})=>{const o=m.getelement(e);o&&o.addEventListener(t,n)})},initCouponSettings:()=>{console.log("Coupon settings initialized");const e=document.querySelector(".table-responsive");if(e&&!document.querySelector("#coupon-search")){const t=document.createElement("div");t.style.marginBottom="15px",t.innerHTML='\n            <input type="text" id="coupon-search" placeholder="Search coupons..."\n                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">\n        ',e.parentNode.insertBefore(t,e),document.getElementById("coupon-search").addEventListener("input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".table tbody tr").forEach(e=>{e.style.display=e.textContent.toLowerCase().includes(t)?"":"none"})})}}}});
