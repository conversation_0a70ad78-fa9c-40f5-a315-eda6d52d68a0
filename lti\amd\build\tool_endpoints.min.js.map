{"version": 3, "file": "tool_endpoints.min.js", "sources": ["../src/tool_endpoints.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n/**\n * Module supporting the dynamic and manual registration URLs in the tool registration admin setting.\n *\n * @module     enrol_lti/tool_endpoints\n * @copyright  2021 Jake <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport 'core/copy_to_clipboard';\n\n/**\n * DOM Selectors.\n * @type {{URL_VALUE: string}}\n */\nconst SELECTORS = {\n    URL_VALUE: '[id^=\"lti_tool_endpoint_url_\"]',\n};\n\n/**\n * Focus handler for the registration URL field, enabling auto select of text on click.\n *\n * @param {Event} event a click event.\n */\nconst focusURLHandler = (event) => {\n    const triggerElement = event.target.closest(SELECTORS.URL_VALUE);\n    if (triggerElement === null) {\n        return;\n    }\n    event.preventDefault();\n\n    triggerElement.select();\n};\n\n/**\n * Initialise the tool registration page, attaching handlers, etc.\n */\nexport const init = () => {\n    // Event delegation supporting the select on focus behaviour (with text selection permitted on subsequent clicks).\n    document.addEventListener('focusin', focusURLHandler);\n};\n"], "names": ["SELECTORS", "focusURLHandler", "event", "triggerElement", "target", "closest", "preventDefault", "select", "document", "addEventListener"], "mappings": ";;;;;;;;MA2BMA,oBACS,iCAQTC,gBAAmBC,cACfC,eAAiBD,MAAME,OAAOC,QAAQL,qBACrB,OAAnBG,iBAGJD,MAAMI,iBAENH,eAAeI,yBAMC,KAEhBC,SAASC,iBAAiB,UAAWR"}