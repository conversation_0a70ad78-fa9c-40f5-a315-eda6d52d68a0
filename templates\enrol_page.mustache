{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_enrol/enrol_page

    Recommended template for displaying the enrolment plugin call-to-action on the enrol/index.php page

    Usually to be used with the \core_enrol\output\enrol_page and returned from the enrol_plugin::enrol_page_hook()

    Example context (json):
    {
        "enrol": "self",
        "instanceid": 1,
        "header": "Self-enrolment",
        "body": "You can enrol yourself in this course.",
        "hasbuttons": true,
        "buttons": [{
            "method" : "get",
            "id": "buttonid-123",
            "type": "primary",
            "url" : "#",
            "label" : "Enrol me"
        }]
    }
}}
<div class="box generalbox mb-3 enrol-instance" data-enrol="{{enrol}}" data-instanceid="{{instanceid}}">
    <div class="card">
        {{#header}}<div class="card-header"><h3 class="mb-0">{{{header}}}</h3></div>{{/header}}
        <div class="card-body">{{{body}}}</div>
        {{#hasbuttons}}
        <div class="card-footer">
            {{#buttons}}
                {{> core/single_button }}
            {{/buttons}}
        </div>
        {{/hasbuttons}}
    </div>
</div>
