{"version": 3, "file": "quickenrolment.min.js", "sources": ["../src/quickenrolment.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Quick enrolment AMD module.\n *\n * @module     enrol_manual/quickenrolment\n * @copyright  2016 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport * as DynamicTable from 'core_table/dynamic';\nimport * as Str from 'core/str';\nimport * as Toast from 'core/toast';\nimport Config from 'core/config';\nimport Fragment from 'core/fragment';\nimport ModalEvents from 'core/modal_events';\nimport Notification from 'core/notification';\nimport jQuery from 'jquery';\nimport Pending from 'core/pending';\nimport Prefetch from 'core/prefetch';\nimport ModalSaveCancel from 'core/modal_save_cancel';\n\nconst Selectors = {\n    cohortSelector: \"#id_cohortlist\",\n    triggerButtons: \".enrolusersbutton.enrol_manual_plugin [type='submit']\",\n    unwantedHiddenFields: \"input[value='_qf__force_multiselect_submission']\",\n    buttonWrapper: '[data-region=\"wrapper\"]',\n};\n\n/**\n * Get the content of the body for the specified context.\n *\n * @param {Number} contextId\n * @returns {Promise}\n */\nconst getBodyForContext = contextId => {\n    return Fragment.loadFragment('enrol_manual', 'enrol_users_form', contextId, {});\n};\n\n/**\n * Get the dynamic table for the button.\n *\n * @param {HTMLElement} element\n * @returns {HTMLElement}\n */\nconst getDynamicTableForElement = element => {\n    const wrapper = element.closest(Selectors.buttonWrapper);\n\n    return DynamicTable.getTableFromId(wrapper.dataset.tableUniqueid);\n};\n\n/**\n * Register the event listeners for this contextid.\n *\n * @param {Number} contextId\n */\nconst registerEventListeners = contextId => {\n    document.addEventListener('click', e => {\n        if (e.target.closest(Selectors.triggerButtons)) {\n            e.preventDefault();\n\n            showModal(getDynamicTableForElement(e.target), contextId);\n\n            return;\n        }\n    });\n};\n\n/**\n * Display the modal for this contextId.\n *\n * @param {HTMLElement} dynamicTable The table to beb refreshed when changes are made\n * @param {Number} contextId\n * @returns {Promise}\n */\nconst showModal = (dynamicTable, contextId) => {\n    const pendingPromise = new Pending('enrol_manual/quickenrolment:showModal');\n\n    return ModalSaveCancel.create({\n        large: true,\n        title: Str.get_string('enrolusers', 'enrol_manual'),\n        body: getBodyForContext(contextId),\n        buttons: {\n            save: Str.get_string('enrolusers', 'enrol_manual'),\n        },\n        show: true,\n    })\n    .then(modal => {\n        modal.getRoot().on(ModalEvents.save, e => {\n            // Trigger a form submission, so that any mform elements can do final tricks before the form submission\n            // is processed.\n            // The actual submit even tis captured in the next handler.\n\n            e.preventDefault();\n            modal.getRoot().find('form').submit();\n        });\n\n        modal.getRoot().on('submit', 'form', e => {\n            e.preventDefault();\n\n            submitFormAjax(dynamicTable, modal);\n        });\n\n        modal.getRoot().on(ModalEvents.hidden, () => {\n            modal.destroy();\n        });\n\n        return modal;\n    })\n    .then(modal => Promise.all([modal, modal.getBodyPromise()]))\n    .then(([modal, body]) => {\n        if (body.get(0).querySelector(Selectors.cohortSelector)) {\n            return modal.setSaveButtonText(Str.get_string('enroluserscohorts', 'enrol_manual')).then(() => modal);\n        }\n\n        return modal;\n    })\n    .then(modal => {\n        pendingPromise.resolve();\n\n        return modal;\n    })\n    .catch(Notification.exception);\n};\n\n/**\n * Submit the form via ajax.\n *\n * @param {HTMLElement} dynamicTable\n * @param {Object} modal\n */\nconst submitFormAjax = (dynamicTable, modal) => {\n    // Note: We use a jQuery object here so that we can use its serialize functionality.\n    const form = modal.getRoot().find('form');\n\n    // Before send the data through AJAX, we need to parse and remove some unwanted hidden fields.\n    // This hidden fields are added automatically by mforms and when it reaches the AJAX we get an error.\n    form.get(0).querySelectorAll(Selectors.unwantedHiddenFields).forEach(hiddenField => hiddenField.remove());\n\n    modal.hide();\n    modal.destroy();\n\n    jQuery.ajax(\n        `${Config.wwwroot}/enrol/manual/ajax.php?${form.serialize()}`,\n        {\n            type: 'GET',\n            processData: false,\n            contentType: \"application/json\",\n        }\n    )\n    .then(response => {\n        if (response.error) {\n            throw new Error(response.error);\n        }\n\n        return response.count;\n    })\n    .then(count => {\n        return Promise.all([\n            Str.get_string('totalenrolledusers', 'enrol', count),\n            DynamicTable.refreshTableContent(dynamicTable),\n        ]);\n    })\n    .then(([notificationBody]) => notificationBody)\n    .then(notificationBody => Toast.add(notificationBody))\n    .catch(error => {\n        Notification.addNotification({\n            message: error.message,\n            type: 'error',\n        });\n    });\n};\n\n/**\n * Set up quick enrolment for the manual enrolment plugin.\n *\n * @param {Number} contextid The context id to setup for\n */\nexport const init = ({contextid}) => {\n    registerEventListeners(contextid);\n\n    Prefetch.prefetchStrings('enrol_manual', [\n        'enrolusers',\n        'enroluserscohorts',\n    ]);\n\n    Prefetch.prefetchString('enrol', 'totalenrolledusers');\n};\n"], "names": ["Selectors", "getBodyForContext", "contextId", "Fragment", "loadFragment", "registerEventListeners", "document", "addEventListener", "e", "target", "closest", "preventDefault", "showModal", "element", "wrapper", "DynamicTable", "getTableFromId", "dataset", "tableUniqueid", "getDynamicTableForElement", "dynamicTable", "pendingPromise", "Pending", "ModalSaveCancel", "create", "large", "title", "Str", "get_string", "body", "buttons", "save", "show", "then", "modal", "getRoot", "on", "ModalEvents", "find", "submit", "submitFormAjax", "hidden", "destroy", "Promise", "all", "getBodyPromise", "_ref", "get", "querySelector", "setSaveButtonText", "resolve", "catch", "Notification", "exception", "form", "querySelectorAll", "for<PERSON>ach", "hiddenField", "remove", "hide", "ajax", "Config", "wwwroot", "serialize", "type", "processData", "contentType", "response", "error", "Error", "count", "refreshTableContent", "_ref2", "notificationBody", "Toast", "add", "addNotification", "message", "_ref3", "contextid", "prefetchStrings", "prefetchString"], "mappings": ";;;;;;;ykBAkCMA,yBACc,iBADdA,yBAEc,wDAFdA,+BAGoB,mDAHpBA,wBAIa,0BASbC,kBAAoBC,WACfC,kBAASC,aAAa,eAAgB,mBAAoBF,UAAW,IAoB1EG,uBAAyBH,YAC3BI,SAASC,iBAAiB,SAASC,OAC3BA,EAAEC,OAAOC,QAAQV,iCACjBQ,EAAEG,sBAEFC,UAhBsBC,CAAAA,gBACxBC,QAAUD,QAAQH,QAAQV,gCAEzBe,aAAaC,eAAeF,QAAQG,QAAQC,gBAajCC,CAA0BX,EAAEC,QAASP,eAcrDU,UAAY,CAACQ,aAAclB,mBACvBmB,eAAiB,IAAIC,iBAAQ,gDAE5BC,2BAAgBC,OAAO,CAC1BC,OAAO,EACPC,MAAOC,IAAIC,WAAW,aAAc,gBACpCC,KAAM5B,kBAAkBC,WACxB4B,QAAS,CACLC,KAAMJ,IAAIC,WAAW,aAAc,iBAEvCI,MAAM,IAETC,MAAKC,QACFA,MAAMC,UAAUC,GAAGC,sBAAYN,MAAMvB,IAKjCA,EAAEG,iBACFuB,MAAMC,UAAUG,KAAK,QAAQC,YAGjCL,MAAMC,UAAUC,GAAG,SAAU,QAAQ5B,IACjCA,EAAEG,iBAEF6B,eAAepB,aAAcc,UAGjCA,MAAMC,UAAUC,GAAGC,sBAAYI,QAAQ,KACnCP,MAAMQ,aAGHR,SAEVD,MAAKC,OAASS,QAAQC,IAAI,CAACV,MAAOA,MAAMW,qBACxCZ,MAAKa,WAAEZ,MAAOL,kBACPA,KAAKkB,IAAI,GAAGC,cAAchD,0BACnBkC,MAAMe,kBAAkBtB,IAAIC,WAAW,oBAAqB,iBAAiBK,MAAK,IAAMC,QAG5FA,SAEVD,MAAKC,QACFb,eAAe6B,UAERhB,SAEViB,MAAMC,sBAAaC,YASlBb,eAAiB,CAACpB,aAAcc,eAE5BoB,KAAOpB,MAAMC,UAAUG,KAAK,QAIlCgB,KAAKP,IAAI,GAAGQ,iBAAiBvD,gCAAgCwD,SAAQC,aAAeA,YAAYC,WAEhGxB,MAAMyB,OACNzB,MAAMQ,0BAECkB,eACAC,gBAAOC,0CAAiCR,KAAKS,aAChD,CACIC,KAAM,MACNC,aAAa,EACbC,YAAa,qBAGpBjC,MAAKkC,cACEA,SAASC,YACH,IAAIC,MAAMF,SAASC,cAGtBD,SAASG,SAEnBrC,MAAKqC,OACK3B,QAAQC,IAAI,CACfjB,IAAIC,WAAW,qBAAsB,QAAS0C,OAC9CvD,aAAawD,oBAAoBnD,kBAGxCa,MAAKuC,YAAEC,+BAAsBA,oBAC7BxC,MAAKwC,kBAAoBC,MAAMC,IAAIF,oBACnCtB,OAAMiB,8BACUQ,gBAAgB,CACzBC,QAAST,MAAMS,QACfb,KAAM,4BAUEc,YAACC,UAACA,iBAClB1E,uBAAuB0E,6BAEdC,gBAAgB,eAAgB,CACrC,aACA,wCAGKC,eAAe,QAAS"}