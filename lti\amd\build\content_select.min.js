define("enrol_lti/content_select",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0;_exports.init=()=>{document.addEventListener("change",(e=>{if(e.target.matches("input[type='checkbox'][name^='modules']")){const value=e.target.value,gradecheckbox=document.querySelector("input[type='checkbox'][name^='grades'][value='"+value+"']");gradecheckbox&&(gradecheckbox.checked=e.target.checked)}if(e.target.matches("input[type='checkbox'][name^='grades']")){const value=e.target.value,modcheckbox=document.querySelector("input[type='checkbox'][name^='modules'][value='"+value+"']");e.target.checked&&(modcheckbox.checked=!0)}}))}}));

//# sourceMappingURL=content_select.min.js.map